import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/utils.dart';

class LoginHeader extends StatelessWidget {
  final Widget? child;
  const LoginHeader({super.key, this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Utils.cachedImageWrapper(
            'image/img-login-header.png',
            alignment: Alignment.topCenter,
          ),
        ),
        child ?? Container(),
        SizedBox(width: Get.width, height: 200),
      ],
    );
  }
}

import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/promosi_agent_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PromosiBMTabController extends BaseControllers {
  Rx<PromosiAgentModel> promosiData = Rx(
    PromosiAgentModel(
      agentCode: '',
      remark: '',
      toAchieveRemark: '',
      netApe: MetricData(target: 0, aktual: 0, kurang: 0),
      netApeGroup: MetricData(target: 0, aktual: 0, kurang: 0),
      agentCount: MetricData(target: 0, aktual: 0, kurang: 0),
      newAgentCount: MetricData(target: 0, aktual: 0, kurang: 0),
      leaderCount: null, // Optional field initialized as null
      persistensi: MetricData(target: 0, aktual: 0, kurang: 0),
      pelatihan: PelatihanData(completed: 0, total: 0),
      status: '',
      statusLisensiAAJI: '',
      statusLisensiAASI: '',
      kekurangan: [],
    ),
  );
  late SharedPreferences prefs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  fetchPromosiAgentData() async {
    setLoading(true);
    prefs = await SharedPreferences.getInstance();

    final agentCode = prefs.getString(kStorageAgentCode);
    final userLevel = prefs.getString(kStorageUserLevel);
    await api.getPromosiAgent(
      controllers: this,
      code: kReqGetPromosiAgent,
      params: "agentCode=$agentCode",
      userLevel: userLevel,
    );
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);
    parseData(response);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = response.statusText ?? 'Error loading data';
  }

  void parseData(response) {
    if (response == null) {
      return;
    }
    for (var item in response) {
      PromosiAgentModel data = PromosiAgentModel.fromJson(item);
      promosiData.value = data;
      break;
    }
  }

  // Helper method to format percentage values
  String formatPercentage(double value) {
    return '${value.toStringAsFixed(0)}%';
  }

  // Helper method to format currency values
  String formatCurrency(double value) {
    if (value >= 1000000000) {
      return 'Rp${(value / 1000000000).toStringAsFixed(1)} M';
    } else if (value >= 1000000) {
      return 'Rp${(value / 1000000).toStringAsFixed(1)} Jt';
    } else {
      return 'Rp${value.toStringAsFixed(0)}';
    }
  }
}

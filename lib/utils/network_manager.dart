import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

class NetworkManager extends GetxController {
  final _isOnline = true.obs;
  final _isConnectionPoor = false.obs;
  final _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _subscription;

  static const int _timeoutSeconds = 2; // Reduced for faster offline detection
  static const String _testHost = 'google.com'; // atau API endpoint Anda
  static const int _poorConnectionThreshold =
      1500; // 1.5 detik dalam milliseconds

  bool get isOnline => _isOnline.value && !_isConnectionPoor.value;

  @override
  void onInit() {
    super.onInit();
    checkConnectivity();
    _subscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
    );
  }

  Future<bool> checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
      return _isOnline.value && !_isConnectionPoor.value;
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error checking connectivity: $e');
      } catch (_) {
        log('Error checking connectivity: $e'); // Fallback to direct log
      }

      // Add retry mechanism for connectivity check after Shorebird updates
      try {
        await Future.delayed(const Duration(milliseconds: 1000));
        final retryResult = await _connectivity.checkConnectivity();
        await _updateConnectionStatus(retryResult);
        return _isOnline.value && !_isConnectionPoor.value;
      } catch (retryError) {
        try {
          Get.find<LoggerService>().log(
            'Retry connectivity check also failed: $retryError',
          );
        } catch (_) {
          log('Retry connectivity check also failed: $retryError');
        }
        return false;
      }
    }
  }

  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    final wasOnline = _isOnline.value;
    _isOnline.value = result != ConnectivityResult.none;

    if (_isOnline.value) {
      // Cek kualitas koneksi hanya jika terdeteksi online
      await _checkConnectionQuality();
    } else {
      _isConnectionPoor.value =
          false; // Reset poor connection state when offline
    }

    // Tampilkan notifikasi hanya jika status berubah
    if (_isOnline.value != wasOnline || _isConnectionPoor.value) {
      _showConnectionStatus();
    }
  }

  void _showConnectionStatus() {
    if (!_isOnline.value) {
      Get.find<LoggerService>().log('Network connection lost');
      _manageFirestoreNetwork(false);
    } else if (_isConnectionPoor.value) {
      Get.find<LoggerService>().log(
        'Poor network connection, switching to offline mode',
      );
      _manageFirestoreNetwork(false);
    } else {
      Get.find<LoggerService>().log('Network connection restored');
      _manageFirestoreNetwork(true);
    }
  }

  void _manageFirestoreNetwork(bool enable) {
    try {
      final firestoreServices = Get.find<FirestoreServices>();
      if (enable) {
        firestoreServices.enableNetwork();
      } else {
        firestoreServices.disableNetwork();
      }
    } catch (e) {
      Get.find<LoggerService>().log(
        'Failed to update Firestore network status: $e',
      );
    }
  }

  Future<void> _checkConnectionQuality() async {
    try {
      final stopwatch = Stopwatch()..start();

      if (kIsWeb) {
        // On web, use a simple HTTP request to check connectivity
        final response = await GetConnect()
            .get('https://$_testHost', headers: {'Cache-Control': 'no-cache'})
            .timeout(Duration(seconds: _timeoutSeconds));

        if (response.isOk) {
          final pingTime = stopwatch.elapsedMilliseconds;
          _isConnectionPoor.value = pingTime > _poorConnectionThreshold;
          Get.find<LoggerService>().log(
            'Connection quality check: ${pingTime}ms',
          );
        } else {
          _isConnectionPoor.value = true;
        }
      } else {
        // On mobile, use InternetAddress.lookup
        final result = await InternetAddress.lookup(
          _testHost,
        ).timeout(Duration(seconds: _timeoutSeconds));

        if (result.isNotEmpty) {
          final pingTime = stopwatch.elapsedMilliseconds;
          _isConnectionPoor.value = pingTime > _poorConnectionThreshold;
          Get.find<LoggerService>().log(
            'Connection quality check: ${pingTime}ms',
          );
        }
      }
    } catch (e) {
      Get.find<LoggerService>().log('Connection quality check failed: $e');
      _isConnectionPoor.value = true;
    }
  }

  // Method publik untuk mengecek kualitas koneksi secara manual
  Future<bool> checkConnectionQuality() async {
    if (!_isOnline.value) return false;
    await _checkConnectionQuality();
    return !_isConnectionPoor.value;
  }

  @override
  void onClose() {
    _subscription.cancel();
    super.onClose();
  }
}

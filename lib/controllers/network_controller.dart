// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class AppController extends GetxController {
  final RxBool isConnected = true.obs;
  late StreamSubscription _subscription;
  // ignore: prefer_typing_uninitialized_variables
  var connectionChecker;

  @override
  Future<void> onInit() async {
    super.onInit();

    if (!kIsWeb) {
      connectionChecker = InternetConnectionChecker.createInstance();
      _subscription = connectionChecker.onStatusChange.listen((
        InternetConnectionStatus status,
      ) {
        if (status == InternetConnectionStatus.connected) {
          isConnected.value = true;
          // print('Connected to the internet');
        } else {
          isConnected.value = false;
          // print('Disconnected from the internet');
        }
      });
    }
  }

  @override
  void onClose() {
    _subscription.cancel();
    super.onClose();
  }

  void retry() async {
    isConnected.value = await connectionChecker.hasConnection;
  }
}

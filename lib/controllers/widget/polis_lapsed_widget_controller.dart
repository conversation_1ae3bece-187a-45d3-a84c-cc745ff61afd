import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/policy_lapsed_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PolisLapsedWidgetController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the policy lapsed data
  RxList<PolicyLapsedModel> policyLapsedList = <PolicyLapsedModel>[].obs;

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchPolicyLapsedData();
  }

  // Fetch policy lapsed data from API
  Future<void> fetchPolicyLapsedData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final agentCode = prefs.getString(kStorageAgentCode);

      if (agentCode != null) {
        await api.getPolicyLapsed(controllers: this, params: "agentCode=$agentCode&valueStatus=ALL&withDownline=0");
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);

    parsePolicyLapsedData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value = 'Failed to load policy lapsed data';
    setLoading(false);
  }

  // Parse the policy lapsed response data
  void parsePolicyLapsedData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <PolicyLapsedModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(PolicyLapsedModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      policyLapsedList.assignAll(tempList);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  // Refresh data
  void refreshData() {
    fetchPolicyLapsedData();
  }
}

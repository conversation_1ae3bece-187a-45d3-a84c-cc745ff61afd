import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/inbox/inbox_list_controller.dart';
import 'package:pdl_superapp/models/response/detail_inbox_response.dart'
    as detailresp;
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/utils/keys.dart';

class DetailInboxController extends BaseControllers {
  final inbox = Rxn<InboxModel>();
  final detailInboxResp = Rxn<detailresp.DetailInboxResponse>();
  final agent = Rxn<detailresp.User>();
  InboxListController listInboxCtrl = Get.find();
  RxBool isFromTrash = false.obs;

  @override
  void onInit() {
    inbox.value = Get.arguments['data'];
    isFromTrash.value = Get.arguments['isFromTrash'];
    reqDetailInbox(id: '${inbox.value?.id}');
    super.onInit();
  }

  Future<void> reqDetailInbox({required String id}) async {
    setLoading(true);
    await api.getDetailInbox(controllers: this, id: id, code: kReqDetailInbox);
  }

  Future<void> reqReadInbox() async {
    await api.postReadInbox(
      controllers: this,
      code: kReqReadInbox,
      ids: ['${inbox.value?.id}'],
    );
  }

  Future<void> reqBulkArchiveInbox() async {
    await api.postArchiveInbox(
      controllers: this,
      code: kReqArchiveInbox,
      ids: ['${inbox.value?.id}'],
    );
  }

  Future<void> reqBulkDeleteInbox() async {
    await api.deleteInbox(
      isHardDelete: isFromTrash.value,
      controllers: this,
      code: kReqDeleteInbox,
      ids: ['${inbox.value?.id}'],
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqDetailInbox:
        detailInboxResp.value = detailresp.DetailInboxResponse.fromJson(
          response,
        );
        agent.value =
            detailInboxResp.value?.detailData?.approvalHeader?.requestBy;
      case kReqReadInbox:
        final index = listInboxCtrl.inboxes.indexWhere(
          (element) => '${element.id}' == '${inbox.value?.id}',
        );
        listInboxCtrl.inboxes[index].isRead =
            !listInboxCtrl.inboxes[index].isRead!;
        listInboxCtrl.inboxes.refresh();
        listInboxCtrl.reqGetUnreadCountInbox();
        Get.back();
      case kReqArchiveInbox:
        final index = listInboxCtrl.inboxes.indexWhere(
          (element) => '${element.id}' == '${inbox.value?.id}',
        );
        listInboxCtrl.inboxes.removeAt(index);
        listInboxCtrl.inboxes.refresh();
        Get.back();
      case kReqDeleteInbox:
        final index = listInboxCtrl.inboxes.indexWhere(
          (element) => '${element.id}' == '${inbox.value?.id}',
        );
        listInboxCtrl.inboxes.removeAt(index);
        listInboxCtrl.inboxes.refresh();
        Get.back();
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    Get.snackbar(
      'Gagal',
      response.body['message'] ?? 'Terjadi Kesalahan harap ulangi kembali',
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

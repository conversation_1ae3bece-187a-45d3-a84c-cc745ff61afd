import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:pdl_superapp/routes/app_routes.dart';

import '../widget/polis_jatuh_tempo_page.dart';

class PolisJatuhTempoWidget extends StatelessWidget {
  PolisJatuhTempoWidget({super.key});

  // Use GetX controller
  final PolisJatuhTempoController controller = Get.put(
    PolisJatuhTempoController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        spacing: paddingMedium,
        children: [
          // Show tabs for all roles
          _buildTabSelector(context),

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector for all roles
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToUpcoming(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 0
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 0
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'policy_due_soon_str'.tr,
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToPast(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 1
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 1
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'policy_expired_str'.tr,
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on selected tab
  Widget _buildContent() {
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildUpcomingContent();
      } else {
        return _buildPastContent();
      }
    });
  }

  // Upcoming Content
  Widget _buildUpcomingContent() {
    return Obx(() {
      final isLoading = controller.upcomingController.isLoading.value;
      final hasError = controller.upcomingController.hasError.value;
      final errorMessage = controller.upcomingController.errorMessage.value;

      void onRetry() {
        controller.upcomingController.fetchPolicyOverdueData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(onPressed: onRetry, child: Text('retry_str'.tr)),
            ],
          ),
        );
      }

      if (controller.upcomingController.policyOverdueList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data polis jatuh tempo"),
          ),
        );
      }

      // Show only first 3 records
      final displayedPolicies =
          controller.upcomingController.policyOverdueList.length > 3
              ? controller.upcomingController.policyOverdueList.sublist(0, 3)
              : controller.upcomingController.policyOverdueList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedPolicies.length,
            padding: EdgeInsets.zero,
            separatorBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: paddingMedium),
                  child: const Divider(height: 1),
                ),
            itemBuilder: (context, index) {
              final policy = displayedPolicies[index];
              return PolicyHolderItem(
                name: policy.policyHolder,
                policyNumber: policy.policyNumber,
                dueDate: formatDate(policy.dueDate),
                status: "ENDING", // Upcoming policies are always ENDING
              );
            },
          ),

          // Lihat Detail and Refresh buttons
          Column(
            children: [
              // Lihat Detail button
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: paddingMedium),
                child: PdlButton(
                  title: 'display_detail'.tr,
                  onPressed: () {
                    // Navigate to detail page
                    final agentCode = controller.agentCode ?? '';
                    Get.toNamed(
                      '${Routes.POLIS_JATUH_TEMPO}?agentCode=$agentCode&type=${controller.selectedSection.value}',
                    );
                  },
                ),
              ),

              // Refresh button
              TextButton(
                onPressed: onRetry,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: paddingSmall),
                    Text('refresh_str'.tr),
                  ],
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  // Past Content
  Widget _buildPastContent() {
    return Obx(() {
      final isLoading = controller.pastController.isLoading.value;
      final hasError = controller.pastController.hasError.value;
      final errorMessage = controller.pastController.errorMessage.value;

      void onRetry() {
        controller.pastController.fetchPolicyOverdueData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(onPressed: onRetry, child: Text('retry_str'.tr)),
            ],
          ),
        );
      }

      if (controller.pastController.policyOverdueList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data polis jatuh tempo"),
          ),
        );
      }

      // Show only first 3 records
      final displayedPolicies =
          controller.pastController.policyOverdueList.length > 3
              ? controller.pastController.policyOverdueList.sublist(0, 3)
              : controller.pastController.policyOverdueList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedPolicies.length,
            padding: EdgeInsets.zero,
            separatorBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: paddingMedium),
                  child: const Divider(height: 1),
                ),

            itemBuilder: (context, index) {
              final policy = displayedPolicies[index];
              return PolicyHolderItem(
                name: policy.policyHolder,
                policyNumber: policy.policyNumber,
                dueDate: formatDate(policy.dueDate),
                status: "LAPSE", // Past policies are always LAPSE
              );
            },
          ),

          // Lihat Detail and Refresh buttons
          Column(
            children: [
              // Lihat Detail button
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: paddingMedium),
                child: PdlButton(
                  title: 'display_detail'.tr,
                  onPressed: () {
                    // Navigate to detail page
                    final agentCode = controller.agentCode ?? '';
                    Get.toNamed(
                      '${Routes.POLIS_JATUH_TEMPO}?agentCode=$agentCode&type=${controller.selectedSection.value}',
                    );
                  },
                ),
              ),

              // Refresh button
              TextButton(
                onPressed: onRetry,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: paddingSmall),
                    Text('refresh_str'.tr),
                  ],
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }
}

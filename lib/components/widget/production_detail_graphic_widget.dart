import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/widget/production/production_detail_graphic_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// Production detail graphic widget for showing agent-specific production charts
/// Similar to GraphicWidget but designed for detail pages with agentCode parameter
class ProductionDetailGraphicWidget extends StatefulWidget {
  /// The agent code for filtering data
  final String agentCode;

  const ProductionDetailGraphicWidget({super.key, required this.agentCode});

  @override
  State<ProductionDetailGraphicWidget> createState() => _ProductionDetailGraphicWidgetState();
}

class _ProductionDetailGraphicWidgetState extends State<ProductionDetailGraphicWidget> {
  late final ProductionDetailGraphicController controller;
  final ScrollController monthlyScrollController = ScrollController();
  final ScrollController yearlyScrollController = ScrollController();

  // Untuk custom scrollbar
  double _dragStartX = 0.0;
  double _initialScrollPosition = 0.0;

  // Get the active scroll controller based on current filter
  ScrollController get activeScrollController =>
      controller.currentFilterType.value == kSwitchMonthly ? monthlyScrollController : yearlyScrollController;

  @override
  void initState() {
    super.initState();

    // Initialize controller with agentCode
    controller = Get.put(ProductionDetailGraphicController(), tag: 'production_detail_graphic_${widget.agentCode}');

    // Set agentCode
    controller.agentCode.value = widget.agentCode;

    // Tambahkan listener untuk memperbarui UI saat scrolling
    monthlyScrollController.addListener(() {
      setState(() {
        // Trigger rebuild untuk memperbarui posisi scrollbar
      });
    });
    yearlyScrollController.addListener(() {
      setState(() {
        // Trigger rebuild untuk memperbarui posisi scrollbar
      });
    });

    // Listen to filter changes to reset scroll position when switching to yearly view
    ever(controller.currentFilterType, (value) {
      if (value == kSwitchYearly && yearlyScrollController.hasClients) {
        yearlyScrollController.jumpTo(0); // Reset to beginning when switching to yearly
      }
    });

    // Fetch data
    controller.fetchData();
  }

  @override
  void dispose() {
    monthlyScrollController.dispose();
    yearlyScrollController.dispose();
    // Don't dispose controller here as it might be used elsewhere
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(
            () =>
                controller.monthlyGraphicData.isEmpty && controller.yearlyGraphicData.isEmpty
                    ? const Center(child: CircularProgressIndicator())
                    : _buildChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    // Reset yearly scroll position to ensure it always starts at the beginning
    if (controller.currentFilterType.value == kSwitchYearly) {
      // Use Future.microtask to avoid calling setState during build
      Future.microtask(() => yearlyScrollController.jumpTo(0));
    }

    return Column(
      children: [
        // Graphic widget
        Container(
          margin: const EdgeInsets.only(bottom: 8.0),
          height: 200, // Tinggi tetap untuk grafik
          child: SingleChildScrollView(
            controller:
                controller.currentFilterType.value == kSwitchMonthly
                    ? monthlyScrollController // Use monthly controller for monthly view
                    : yearlyScrollController, // Use yearly controller for yearly view
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            child: Container(
              padding: EdgeInsets.only(
                top: paddingMedium,
                right: controller.currentFilterType.value == kSwitchYearly ? 40.0 : 0.0,
              ),
              width:
                  controller.currentFilterType.value == kSwitchMonthly
                      ? 800 // Width for monthly data (12 months)
                      : controller.years.length <= 5
                      ? Get.width -
                          10 // Use screen width with more padding for 5 or fewer years
                      : controller.years.length * 60, // Increased spacing for more years
              child: AspectRatio(
                aspectRatio:
                    controller.currentFilterType.value == kSwitchMonthly
                        ? 3.0 // Original aspect ratio for monthly view
                        : 2.5, // Slightly taller chart for yearly view
                child: LineChart(
                  LineChartData(
                    // Add tooltip configuration to show formatted currency values
                    lineTouchData: LineTouchData(
                      touchTooltipData: LineTouchTooltipData(
                        tooltipRoundedRadius: 4,
                        tooltipPadding: const EdgeInsets.all(8),
                        tooltipMargin: 0,
                        showOnTopOfTheChartBoxArea: true,
                        getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                          return touchedBarSpots.map((barSpot) {
                            // Format the value using Utils.currencyFormatters
                            final formattedValue = Utils.currencyFormatters(data: barSpot.y.toString(), currency: 'Rp');

                            // Get the label (month or year)
                            String label = '';
                            if (controller.currentFilterType.value == kSwitchMonthly) {
                              if (barSpot.x.toInt() >= 0 && barSpot.x.toInt() < controller.months.length) {
                                label = controller.months[barSpot.x.toInt()];
                              }
                            } else {
                              if (barSpot.x.toInt() >= 0 && barSpot.x.toInt() < controller.years.length) {
                                label = controller.years[barSpot.x.toInt()].toString();
                              }
                            }

                            return LineTooltipItem(
                              '$label: $formattedValue',
                              const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                            );
                          }).toList();
                        },
                        fitInsideHorizontally: true,
                        fitInsideVertically: true,
                      ),
                      handleBuiltInTouches: true,
                      touchSpotThreshold: 20,
                    ),
                    clipData: FlClipData.all(),
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: true,
                      horizontalInterval: controller.maxY.value > 0 ? controller.maxY.value / 5 : 1000,
                      verticalInterval: 1,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(color: const Color.fromRGBO(128, 128, 128, 0.3), strokeWidth: 1);
                      },
                      getDrawingVerticalLine: (value) {
                        return FlLine(color: const Color.fromRGBO(128, 128, 128, 0.3), strokeWidth: 1);
                      },
                    ),
                    titlesData: FlTitlesData(
                      show: true,
                      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 30, // Increased space for labels
                          interval: 1,
                          getTitlesWidget: _bottomTitleWidgets,
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          interval:
                              controller.maxY.value > 0
                                  ? controller.maxY.value / 5
                                  : 1000, // Sesuaikan dengan grid horizontal
                          getTitlesWidget: _leftTitleWidgets,
                          reservedSize: 50, // Increased space for currency values
                        ),
                      ),
                    ),
                    borderData: FlBorderData(show: true, border: Border.all(color: Colors.transparent)),
                    minX: 0, // No padding on the left
                    maxX:
                        controller.currentFilterType.value == kSwitchMonthly
                            ? 11.5 // 12 months (0-11) with padding on the right
                            : (controller.years.length - 1 + 0.5)
                                .toDouble(), // Number of years - 1 with padding on the right
                    minY: controller.minY.value,
                    maxY: controller.maxY.value,
                    lineBarsData: [
                      LineChartBarData(
                        spots: controller.chartData,
                        isCurved: true,
                        gradient: LinearGradient(
                          colors: [
                            controller.chartColor.value,
                            controller.chartColor.value.withAlpha(179), // 0.7 opacity
                          ],
                        ),
                        barWidth: 5,
                        isStrokeCapRound: true,
                        dotData: const FlDotData(show: true),
                        belowBarData: BarAreaData(
                          show: true,
                          gradient: LinearGradient(
                            colors: [
                              controller.chartColor.value.withAlpha(77), // 0.3 opacity
                              controller.chartColor.value.withAlpha(26), // 0.1 opacity
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),

        // Custom scrollbar di bawah graphic widget - only show for monthly view
        Visibility(visible: controller.currentFilterType.value == kSwitchMonthly, child: _buildCustomScrollbar()),
      ],
    );
  }

  Widget _buildCustomScrollbar() {
    return Container(
      height: 10, // Tinggi container untuk scrollbar
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Hitung lebar dan posisi thumb berdasarkan scroll position
          final double viewportWidth = constraints.maxWidth;
          final double contentWidth = 800; // Width for monthly data

          // Jika konten lebih kecil dari viewport, tidak perlu scrollbar
          if (contentWidth <= viewportWidth) {
            return const SizedBox.shrink();
          }

          // Hitung rasio dan posisi thumb
          final double ratio = viewportWidth / contentWidth;
          final double thumbWidth = viewportWidth * ratio;
          final double maxScrollExtent = contentWidth - viewportWidth;
          final double scrollRatio =
              monthlyScrollController.hasClients ? monthlyScrollController.position.pixels / maxScrollExtent : 0.0;
          final double thumbPosition = (viewportWidth - thumbWidth) * scrollRatio;

          return GestureDetector(
            onHorizontalDragStart: (details) {
              _dragStartX = details.localPosition.dx;
              _initialScrollPosition = monthlyScrollController.position.pixels;
            },
            onHorizontalDragUpdate: (details) {
              final double dragDelta = details.localPosition.dx - _dragStartX;
              final double scrollDelta = dragDelta * (contentWidth / viewportWidth);

              if (monthlyScrollController.hasClients) {
                monthlyScrollController.jumpTo(
                  (_initialScrollPosition + scrollDelta).clamp(0.0, monthlyScrollController.position.maxScrollExtent),
                );
              }
            },
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color.fromRGBO(200, 200, 200, 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Stack(
                children: [
                  Positioned(
                    left: thumbPosition,
                    child: Container(
                      width: thumbWidth,
                      height: 10,
                      decoration: BoxDecoration(
                        color: controller.chartColor.value,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _bottomTitleWidgets(double value, TitleMeta meta) {
    TextStyle style = TextStyle(
      fontWeight: FontWeight.bold,
      fontStyle: Theme.of(context).textTheme.bodySmall?.fontStyle,
    );

    // Check if we're showing monthly or yearly data
    if (controller.currentFilterType.value == kSwitchMonthly) {
      // Only show month name if it's a whole number (month index)
      if (value.toInt() == value && value >= 0 && value < controller.months.length) {
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(controller.months[value.toInt()], style: style, textAlign: TextAlign.center),
        );
      }
    } else {
      // Show year if it's a whole number (year index)
      if (value.toInt() == value && value >= 0 && value < controller.years.length) {
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(controller.years[value.toInt()].toString(), style: style, textAlign: TextAlign.center),
        );
      }
    }

    return const SizedBox.shrink();
  }

  Widget _leftTitleWidgets(double value, TitleMeta meta) {
    TextStyle style = TextStyle(
      fontWeight: FontWeight.bold,
      fontStyle: Theme.of(context).textTheme.bodySmall?.fontStyle,
    );

    if (value == 0) {
      return const Text('');
    }

    // Format the currency value
    String formattedValue = controller.formatCurrency(value);

    return Padding(
      padding: const EdgeInsets.only(right: paddingSmall),
      child: Text(
        formattedValue,
        style: style,
        textAlign: TextAlign.end,
        maxLines: 1, // Ensure it's only one line
      ),
    );
  }
}

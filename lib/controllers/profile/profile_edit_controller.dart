import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/attachement_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/models/combo_category_models.dart';
import 'package:pdl_superapp/models/request_edit_models.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileEditController extends BaseControllers {
  String baseUrl = ConfigReader.getBaseUrl();
  RxBool isEdit = false.obs;

  // Bank data from API
  RxList<ComboCategoryModels> bankData = RxList<ComboCategoryModels>();
  RxList<String> listBank = RxList<String>();

  // Marital Status data from API
  RxList<ComboCategoryModels> maritalStatusData = RxList<ComboCategoryModels>();
  RxList<String> listStatus = RxList<String>();

  // Education data from API
  RxList<ComboCategoryModels> educationData = RxList<ComboCategoryModels>();
  RxList<String> listPendidikan = RxList<String>();

  UserModels currentUserData = UserModels();
  Rx<UserModels> userData = UserModels().obs;
  RxList<RequestEditModels> requestList = RxList();

  // Profile Picture
  RxString currentProfilePicture = ''.obs;

  // Preview image URL for non-BP/BM/BD users (shows immediately after upload)
  RxString previewProfilePictureUrl = ''.obs;

  // Error message for profile picture file size validation
  RxString profilePictureErrorMessage = ''.obs;

  // File Attachement
  RxString currentKtpAttachement = ''.obs;
  RxString currentKKAttachement = ''.obs;
  RxString currentBankAttachement = ''.obs;

  RxList<String> inApprovalList = RxList();
  RxList<Map<String, dynamic>> inApprovalPendingList = RxList();

  RxString userLevel = ''.obs;

  TextEditingController nameTextController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  TextEditingController phoneTextController = TextEditingController();
  TextEditingController agentCodeTextController = TextEditingController();
  TextEditingController branchCodeTextController = TextEditingController();
  TextEditingController levelTextController = TextEditingController();
  TextEditingController bankTextController = TextEditingController();
  TextEditingController bankNumberTextController = TextEditingController();
  TextEditingController addressTextController = TextEditingController();
  TextEditingController maritalTextController = TextEditingController();
  TextEditingController educationTextController = TextEditingController();

  // New text controllers for non-BP/BM/BD roles
  TextEditingController fullNameTextController = TextEditingController();
  TextEditingController usernameTextController = TextEditingController();
  TextEditingController channelTextController = TextEditingController();
  TextEditingController roleNameTextController = TextEditingController();
  TextEditingController branchNameTextController = TextEditingController();
  TextEditingController statusTextController = TextEditingController();

  late SharedPreferences prefs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '-';

    load();
  }

  @override
  void load() async {
    super.load();
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '-';
    await _firstRequest();
  }

  Future<void> _firstRequest() async {
    setLoading(true);

    await loadComboData();

    // Load user profile data first (like home and profile controllers do)
    await api.getProfile(controllers: this, code: kReqGetProfile);

    // Then load profile request status
    await api.getProfileRequestStatus(
      params:
          '?size=10000&approvalStatus=$kApprovalStatusNew&approvalStatus=$kApprovalStatusPending&approvalStatus=$kApprovalStatusWaiting',
      controllers: this,
      code: kReqGetCurrentUpdateProfile,
    );
  }

  /// Load user profile details from API
  void loadUserProfile() async {
    setLoading(true);
    await api.getProfile(controllers: this, code: kReqGetProfile);
  }

  /// Refresh user profile data (can be called manually)
  void refreshUserProfile() async {
    setLoading(true);
    await api.getProfile(controllers: this, code: kReqGetProfile);
  }

  /// Load all combo data from API
  Future<void> loadComboData() async {
    try {
      // Load Bank data
      await api.getComboCategoryById(
        controllers: this,
        key: 'Bank',
        code: kReqGetBankCombo,
        debug: false,
      );

      // Load Marital Status data
      await api.getComboCategoryById(
        controllers: this,
        key: 'MaritalStatus',
        code: kReqGetMaritalStatusCombo,
        debug: false,
      );

      // Load Education data
      await api.getComboCategoryById(
        controllers: this,
        key: 'Education',
        code: kReqGetEducationCombo,
        debug: false,
      );
    } catch (e) {
      debugPrint('Error loading combo data: $e');
      // Fallback to empty lists if API fails
      listBank.value = [''];
      listStatus.value = [''];
      listPendidikan.value = [''];
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetCurrentUpdateProfile:
        parseDataRequest(response);
        setLoading(false); // Only set loading false after both calls complete
        break;
      case kReqGetProfile:
        parseUserProfile(response);
        // Don't set loading false here, wait for the request status call
        break;
      case kReqUploadProfile:
        setLoading(false);
        break;
      case kReqGetBankCombo:
        parseBankData(response);
        break;
      case kReqGetMaritalStatusCombo:
        parseMaritalStatusData(response);
        break;
      case kReqGetEducationCombo:
        parseEducationData(response);
        break;
      case 2200:
        setLoading(false);
        break;
      default:
        parseData(response);
        setLoading(false);
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);

    Get.snackbar(
      'Error',
      response.body['error_description'] ?? 'Terjadi kesalahan',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  parseDataRequest(response) {
    requestList.clear();
    for (int i = 0; i < response['content'].length; i++) {
      RequestEditModels requestData = RequestEditModels.fromJson(
        response['content'][i],
      );
      requestList.add(requestData);
    }

    inApprovalPendingList.clear();
    final tempPendinglist = requestList.where(
      (request) => request.approvalStatus == kApprovalStatusPending,
    );
    for (final item in tempPendinglist) {
      for (final data in item.fieldData) {
        inApprovalPendingList.add({
          'name': data.fieldName ?? '-',
          'id': item.id,
        });
      }
    }

    inApprovalList.clear();
    for (var request in requestList) {
      if ([
        kApprovalStatusNew,
        kApprovalStatusWaiting,
      ].contains(request.approvalStatus)) {
        for (FieldDataModels detail in request.fieldData) {
          if (!inApprovalList.contains(detail.fieldName)) {
            inApprovalList.add(detail.fieldName ?? '-');
          }
        }
      }
    }
  }

  parseUserProfile(response) async {
    try {
      UserModels userProfileData = UserModels.fromJson(response);
      userData.value = userProfileData;
      setInitialData(userProfileData);

      if (userData.value.userType == "AGENT") {
        setLoading(true);
        // Then load profile request status
        await api.getProfileRequestStatus(
          controllers: this,
          code: kReqGetCurrentUpdateProfile,
        );
      }
    } catch (e) {
      debugPrint('Error parsing user profile: $e');
    }
  }

  /// Parse bank data from API response
  parseBankData(response) {
    try {
      bankData.clear();
      listBank.clear();

      // Add empty option first
      listBank.add('');

      if (response is List) {
        for (var item in response) {
          ComboCategoryModels bank = ComboCategoryModels.fromJson(item);
          bankData.add(bank);
          // Use the 'value2' field for display in dropdown (bank name)
          listBank.add(bank.value2 ?? '');
        }
      }

      debugPrint('Bank data loaded: ${listBank.length} items');
    } catch (e) {
      debugPrint('Error parsing bank data: $e');
      // Fallback to empty list
      listBank.value = [''];
    }
  }

  /// Parse marital status data from API response
  parseMaritalStatusData(response) {
    try {
      maritalStatusData.clear();
      listStatus.clear();

      // Add empty option first
      listStatus.add('');

      if (response is List) {
        for (var item in response) {
          ComboCategoryModels status = ComboCategoryModels.fromJson(item);
          maritalStatusData.add(status);
          // Use the 'value' field for display in dropdown
          listStatus.add(status.value ?? '');
        }
      }

      debugPrint('Marital status data loaded: ${listStatus.length} items');
    } catch (e) {
      debugPrint('Error parsing marital status data: $e');
      // Fallback to empty list
      listStatus.value = [''];
    }
  }

  /// Parse education data from API response
  parseEducationData(response) {
    try {
      educationData.clear();
      listPendidikan.clear();

      // Add empty option first
      listPendidikan.add('');

      if (response is List) {
        for (var item in response) {
          ComboCategoryModels education = ComboCategoryModels.fromJson(item);
          educationData.add(education);
          // Use the 'value' field for display in dropdown
          listPendidikan.add(education.value ?? '');
        }
      }

      debugPrint('Education data loaded: ${listPendidikan.length} items');
    } catch (e) {
      debugPrint('Error parsing education data: $e');
      // Fallback to empty list
      listPendidikan.value = [''];
    }
  }

  parseData(response) async {
    // Clear preview for non-BP/BM/BD users after successful save
    if (userLevel.value != kUserLevelBp &&
        userLevel.value != kUserLevelBm &&
        userLevel.value != kUserLevelBd) {
      previewProfilePictureUrl.value = '';
    }

    if (userLevel.value == kUserLevelBp ||
        userLevel.value == kUserLevelBm ||
        userLevel.value == kUserLevelBd) {
      Get.snackbar(
        'Success!',
        'data_in_change_submission_str'.tr,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        'Success!',
        'success_change_data_str'.tr,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
    isEdit.value = false;
    await _firstRequest();
  }

  String setSelectedItem(List<String> item, String current) {
    String result = '';

    if (item.contains(current)) {
      result = current;
    }

    return result;
  }

  /// Get bank display value from key (for setting initial data)
  String getBankDisplayValue(String? bankKey) {
    if (bankKey == null || bankKey.isEmpty) return '';

    for (var bank in bankData) {
      if (bank.key == bankKey) {
        return bank.value2 ?? ''; // Use value2 for bank name
      }
    }

    // If not found in API data, return the original value
    return bankKey;
  }

  /// Get bank key from display value (for saving data)
  String getBankKey(String displayValue) {
    if (displayValue.isEmpty) return '';

    for (var bank in bankData) {
      if (bank.value2 == displayValue) {
        return bank.key ?? '';
      }
    }

    // If not found in API data, return the original value
    return displayValue;
  }

  /// Get marital status display value from key (for setting initial data)
  String getMaritalStatusDisplayValue(String? statusKey) {
    if (statusKey == null || statusKey.isEmpty) return '';

    for (var status in maritalStatusData) {
      if (status.key == statusKey) {
        return status.value ?? '';
      }
    }

    // If not found in API data, return the original value
    return statusKey;
  }

  /// Get marital status key from display value (for saving data)
  String getMaritalStatusKey(String displayValue) {
    if (displayValue.isEmpty) return '';

    for (var status in maritalStatusData) {
      if (status.value == displayValue) {
        return status.key ?? '';
      }
    }

    // If not found in API data, return the original value
    return displayValue;
  }

  /// Get education display value from key (for setting initial data)
  String getEducationDisplayValue(String? educationKey) {
    if (educationKey == null || educationKey.isEmpty) return '';

    for (var education in educationData) {
      if (education.key == educationKey) {
        return education.value ?? '';
      }
    }

    // If not found in API data, return the original value
    return educationKey;
  }

  /// Get education key from display value (for saving data)
  String getEducationKey(String displayValue) {
    if (displayValue.isEmpty) return '';

    for (var education in educationData) {
      if (education.value == displayValue) {
        return education.key ?? '';
      }
    }

    // If not found in API data, return the original value
    return displayValue;
  }

  setInitialData(UserModels userdata) {
    currentUserData = userdata;
    nameTextController.text = userdata.agentName ?? '';
    emailTextController.text = userdata.email ?? '';
    phoneTextController.text = userdata.phoneNumber ?? '';
    agentCodeTextController.text = userdata.agentCode ?? '';
    branchCodeTextController.text =
        "${userdata.branchCode} - ${userdata.branchName}";
    levelTextController.text = userdata.level ?? '';
    bankTextController.text = getBankDisplayValue(userdata.bank);
    bankNumberTextController.text = userdata.bankAccountNumber ?? '';
    addressTextController.text = userdata.address ?? '';
    maritalTextController.text = getMaritalStatusDisplayValue(
      userdata.maritalStatus,
    );
    educationTextController.text = getEducationDisplayValue(userdata.education);

    // Initialize new text controllers for non-BP/BM/BD roles
    fullNameTextController.text = userdata.name ?? '';
    usernameTextController.text = userdata.username ?? '';
    channelTextController.text = userdata.channel ?? '';
    roleNameTextController.text = userdata.roles?.name ?? '';
    branchNameTextController.text = ''; // No prefill as requested
    statusTextController.text = 'aktif'; // Set to "aktif" as requested

    currentProfilePicture.value = userData.value.photo ?? '';
    // Clear any existing preview
    previewProfilePictureUrl.value = '';
  }

  /// Clear preview image for non-BP/BM/BD users
  void clearPreviewImage() {
    if (userLevel.value != kUserLevelBp &&
        userLevel.value != kUserLevelBm &&
        userLevel.value != kUserLevelBd) {
      previewProfilePictureUrl.value = '';
    }
  }

  checkAttachement(context) {
    // check roles fist, kalau diatas bdm up dia cukup cek profile picture ada apa enggak, terus langsung hit api
    // edit profile cms
    if (userLevel.value != kUserLevelBp &&
        userLevel.value != kUserLevelBm &&
        userLevel.value != kUserLevelBd) {
      performChangeProfile();
      return;
    }

    bool isKtpRequired =
        (nameTextController.text.isNotEmpty &&
            currentUserData.agentName != nameTextController.text) ||
        (addressTextController.text.isNotEmpty &&
            currentUserData.address != addressTextController.text);
    bool isBankRequired =
        (bankTextController.text.isNotEmpty &&
            getBankDisplayValue(currentUserData.bank) !=
                bankTextController.text) ||
        (bankNumberTextController.text.isNotEmpty &&
            currentUserData.bankAccountNumber != bankNumberTextController.text);
    bool isKKRequired =
        (addressTextController.text.isNotEmpty &&
            currentUserData.address != addressTextController.text) ||
        (maritalTextController.text.isNotEmpty &&
            getMaritalStatusDisplayValue(currentUserData.maritalStatus) !=
                maritalTextController.text);
    debugPrint("is KK $isKKRequired");
    if (isKtpRequired || isBankRequired || isKKRequired) {
      PdlBottomSheet(
        content: AttachementBottomSheet(
          onFinish: (val) {
            currentKtpAttachement.value = val.ktpUrl ?? '';
            currentKKAttachement.value = val.kkUrl ?? '';
            currentBankAttachement.value = val.bankUrl ?? '';
            performChangeProfile();
          },
          isKtp: isKtpRequired,
          isBank: isBankRequired,
          isKK: isKKRequired,
        ),
        title: 'title_support_document'.tr,
      );
    } else {
      performChangeProfile();
    }
  }

  performChangeProfile() async {
    var data = {};

    // For BP, BM, BD roles
    if (nameTextController.text != '' &&
        currentUserData.agentName != nameTextController.text) {
      data["agentName"] = nameTextController.text;
    }
    if (emailTextController.text != '' &&
        currentUserData.email != emailTextController.text) {
      data["email"] = emailTextController.text;
    }
    if (phoneTextController.text != '' &&
        currentUserData.phoneNumber != phoneTextController.text) {
      data["phoneNumber"] = phoneTextController.text;
    }
    if (bankTextController.text != '' &&
        getBankDisplayValue(currentUserData.bank) != bankTextController.text) {
      data["bank"] = getBankKey(bankTextController.text);
    }
    if (bankNumberTextController.text != '' &&
        currentUserData.bankAccountNumber != bankNumberTextController.text) {
      data["bankAccountNumber"] = bankNumberTextController.text;
    }
    if (addressTextController.text != '' &&
        currentUserData.address != addressTextController.text) {
      data["address"] = addressTextController.text;
    }
    if (maritalTextController.text != '' &&
        getMaritalStatusDisplayValue(currentUserData.maritalStatus) !=
            maritalTextController.text) {
      data["maritalStatus"] = getMaritalStatusKey(maritalTextController.text);
    }
    if (educationTextController.text != '' &&
        getEducationDisplayValue(currentUserData.education) !=
            educationTextController.text) {
      data["education"] = getEducationKey(educationTextController.text);
    }

    // For non-BP/BM/BD roles - all fields are disabled, so no updates needed

    if (currentProfilePicture.value != '') {
      if (userLevel.value != kUserLevelBp &&
          userLevel.value != kUserLevelBm &&
          userLevel.value != kUserLevelBd) {
        data["picture"] = currentProfilePicture.value;
      } else {
        data["photo"] = currentProfilePicture.value;
      }
    }
    // documents attachement
    if (currentBankAttachement.value != '') {
      data["bankAttachment"] = currentBankAttachement.value;
    }
    if (currentKKAttachement.value != '') {
      data["kkAttachment"] = currentKKAttachement.value;
    }
    if (currentKtpAttachement.value != '') {
      data["ktpAttachment"] = currentKtpAttachement.value;
    }
    setLoading(true);

    if (userLevel.value == kUserLevelBp ||
        userLevel.value == kUserLevelBm ||
        userLevel.value == kUserLevelBd) {
      // Create a map to group pending data by ID
      Map<String, List<Map<String, dynamic>>> pendingDataById = {};
      Map<String, dynamic> regularData = {};

      // Separate and group data
      data.forEach((key, value) {
        // Check if this key is in pending list
        var pendingItem = inApprovalPendingList.firstWhereOrNull(
          (item) => item['name'] == key,
        );

        if (pendingItem != null) {
          // Get the ID from pending item
          final id = pendingItem['id'].toString();

          // Create list for this ID if it doesn't exist
          if (!pendingDataById.containsKey(id)) {
            pendingDataById[id] = [];
          }

          // Add the data to the list for this ID
          pendingDataById[id]!.add({'key': key, 'value': value});
        } else {
          regularData[key] = value;

          // Add additional data based on conditions
          if (key == 'address' || key == 'agentName') {
            regularData['ktpAttachment'] = data['ktpAttachment'];
          }
          if (key == 'bank' || key == 'bankAccountNumber') {
            regularData['bankAttachment'] = data['bankAttachment'];
          }
          if (key == 'address' || key == 'maritalStatus') {
            regularData['kkAttachment'] = data['kkAttachment'];
          }
        }
      });

      // Process each group of pending data
      for (var entry in pendingDataById.entries) {
        final id = entry.key;
        final dataList = entry.value;

        // Create a map to send to API
        final apiData = Map<String, dynamic>.fromEntries(
          dataList.map((item) => MapEntry(item['key'], item['value'])),
        );

        await api.patchReviseProfile(
          controllers: this,
          data: apiData,
          id: id,
          code: 2200,
        );
      }

      // Then handle regular data
      if (regularData.isNotEmpty) {
        await api.patchProfile(
          controllers: this,
          data: regularData,
          code: 2200,
        );
      }
      return;
    } else {
      data['name'] = currentUserData.name;
      data['email'] = currentUserData.email ?? '';
      data['phone'] = currentUserData.phone ?? '';
      await api.updateProfileCms(controllers: this, data: data);
      return;
    }
  }

  performUpdateProfilePicture({
    required XFile image,
    required String url,
  }) async {
    try {
      MultipartFile multipartFile;

      if (kIsWeb) {
        // On web, read the file as bytes
        final bytes = await image.readAsBytes();
        multipartFile = MultipartFile(bytes, filename: image.name);
      } else {
        // On mobile, use File object
        multipartFile = MultipartFile(File(image.path), filename: image.name);
      }

      final form = FormData({'file': multipartFile});
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      setLoading(true);
      final response = await GetConnect().post(
        url,
        form,
        headers: {'Authorization': 'Bearer $token'},
        uploadProgress: (percent) {
          // You can update UI instead
        },
      );

      if (response.isOk) {
        setLoading(false);
        String url = response.body['initialPreview'][0];

        // For BP/BM/BD users: Only store URL, no preview
        if (userLevel.value == kUserLevelBp ||
            userLevel.value == kUserLevelBm ||
            userLevel.value == kUserLevelBd) {
          currentProfilePicture.value = url;
        } else {
          // For non-BP/BM/BD users: Store URL and show preview immediately
          currentProfilePicture.value = url;
          previewProfilePictureUrl.value = url;
        }
        // success
      } else {
        setLoading(false);
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? response.body ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
      }
    } catch (e) {
      debugPrint('Error uploading profile picture: $e');
    }
  }

  profileBottomSheet(context, {XFile? profileImage}) {
    if (isEdit.isFalse) {
      return;
    }

    // For non-BP/BM/BD users, show preview if available
    bool isNonAgentUser =
        userLevel.value != kUserLevelBp &&
        userLevel.value != kUserLevelBm &&
        userLevel.value != kUserLevelBd;

    PdlBottomSheet(
      title: 'title_profile_picture'.tr,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: Text(
              'subtitle_profile_picture'.tr,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          SizedBox(height: paddingMedium),
          Container(
            width: 190,
            height: 190,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(190),
              color: kColorGlobalBgGreen,
            ),
            clipBehavior: Clip.hardEdge,
            child: Obx(() {
              // For non-BP/BM/BD users, show preview if available
              bool isNonAgentUser =
                  userLevel.value != kUserLevelBp &&
                  userLevel.value != kUserLevelBm &&
                  userLevel.value != kUserLevelBd;

              if (profileImage != null) {
                // Show selected image preview
                return kIsWeb
                    ? Image.network(
                      profileImage.path,
                      fit: BoxFit.cover,
                      alignment: Alignment.center,
                    )
                    : Image.file(
                      File(profileImage.path),
                      fit: BoxFit.cover,
                      alignment: Alignment.center,
                    );
              } else if (isNonAgentUser &&
                  previewProfilePictureUrl.value.isNotEmpty) {
                // Show uploaded preview for non-BP/BM/BD users
                return CachedNetworkImage(
                  imageUrl: previewProfilePictureUrl.value,
                  fit: BoxFit.cover,
                  alignment: Alignment.center,
                );
              } else if (userData.value.photo != null) {
                // Show original profile photo
                return CachedNetworkImage(
                  imageUrl: userData.value.photo!,
                  fit: BoxFit.cover,
                  alignment: Alignment.center,
                );
              } else {
                // Show initials
                return Center(
                  child: Text(
                    Utils.getInitials(
                      userData.value.agentName ?? userData.value.name ?? '-',
                    ),
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: kColorTextTersierLight,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                );
              }
            }),
          ),
          SizedBox(height: paddingMedium),
          if (profileImage != null && !isNonAgentUser)
            CommonWidgets.errorCard(
              context,
              content: 'title_profile_warning'.tr,
            ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child:
                profileImage != null
                    ? Row(
                      children: [
                        Expanded(
                          child: FilledButton(
                            onPressed: () {
                              Get.back();
                            },
                            style: ButtonStyle(
                              backgroundColor: WidgetStatePropertyAll(
                                Theme.of(context).colorScheme.surface,
                              ),
                              foregroundColor: WidgetStatePropertyAll(
                                Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            child: Text('label_cancel'.tr),
                          ),
                        ),
                        SizedBox(width: paddingMedium),
                        Expanded(
                          child: PdlButton(
                            controller: this,
                            onPressed: () {
                              setLoading(true);
                              performUpdateProfilePicture(
                                image: profileImage,
                                url: '$baseUrl/profile/profile-picture',
                              );
                              Get.back();
                            },
                            title: 'button_save'.tr,
                          ),
                        ),
                      ],
                    )
                    : FilledButton(
                      onPressed: () {
                        Get.back();
                        profileSelectBottom(context);
                      },
                      child: Text('change_photo_str'.tr),
                    ),
          ),
        ],
      ),
    );
  }

  profileSelectBottom(context) {
    // Clear error message when opening bottom sheet
    profilePictureErrorMessage.value = '';

    PdlBottomSheet(
      title: 'title_change_profile'.tr,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          CommonWidgets.bottomSheetOptionCard(
            context,
            title: 'Pilih dari Galery',
            prefix: Utils.cachedSvgWrapper(
              'icon/ic-linear-image.svg',
              width: 24,
            ),
            onTap:
                () => Utils.imagePicker(
                  context,
                  validateFileSize: true,
                  maxSizeMB: 2.0,
                  onSuccess: (val) async {
                    profilePictureErrorMessage.value = '';
                    Get.back();
                    profileBottomSheet(context, profileImage: val);
                  },
                  onError: (String errorMessage) {
                    profilePictureErrorMessage.value = errorMessage;
                  },
                ),
          ),
          Divider(),

          CommonWidgets.bottomSheetOptionCard(
            context,
            title: 'Ambil Foto',
            prefix: Utils.cachedSvgWrapper(
              'icon/ic-linear-camera-minimalistic.svg',
              width: 24,
            ),
            onTap:
                () => Utils.imagePicker(
                  context,
                  isCamera: true,
                  validateFileSize: true,
                  maxSizeMB: 2.0,
                  onSuccess: (val) async {
                    profilePictureErrorMessage.value = '';
                    Get.back();
                    profileBottomSheet(context, profileImage: val);
                  },
                  onError: (String errorMessage) {
                    profilePictureErrorMessage.value = errorMessage;
                  },
                ),
          ),
          Obx(() {
            if (profilePictureErrorMessage.value.isNotEmpty) {
              return Padding(
                padding: const EdgeInsets.only(top: paddingMedium),
                child: Text(
                  profilePictureErrorMessage.value,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }
            return SizedBox.shrink();
          }),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/components/pdl_showcase.dart';
import 'package:pdl_superapp/components/state/empty_state_view.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:showcaseview/showcaseview.dart';

class NotificationPage extends StatelessWidget {
  NotificationPage({super.key});

  final NotificationPageController controller = Get.put(
    NotificationPageController(),
  );

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      builder: (context) {
        showCaseContext = context;
        return BaseDetailPage(
          title: 'notification_str'.tr,
          controller: controller,
          scrollController: controller.scrollController,
          onRefresh: () {
            controller.isReadAll.value = false;
            controller.getInbox(isRefresh: true);
          },
          child: Container(
            width: Get.width,
            padding: EdgeInsets.symmetric(
              horizontal: paddingMedium,
              vertical: paddingMedium,
            ),
            child: PdlShowcase(
              globalKey: controller.tutorData.first.key,
              title: controller.tutorData.first.title,
              description: controller.tutorData.first.description,
              number: controller.tutorData.first.number,
              total: controller.tutorData.length,
              child: Obx(
                () => Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TitleWidget(title: 'notification_str'.tr),
                        ),
                        GestureDetector(
                          onTap:
                              controller.isReadAll.isTrue
                                  ? () => PdlBaseDialog(
                                    context: context,
                                    child: PdlDialogContent(
                                      message: '${'mark_all_as_read_str'.tr}?',
                                      onTap: () {
                                        Get.back();
                                        controller.readAll();
                                      },
                                    ),
                                  )
                                  : null,
                          child: PdlShowcase(
                            globalKey: controller.tutorData.last.key,
                            title: controller.tutorData.last.title,
                            description: controller.tutorData.last.description,
                            number: controller.tutorData.last.number,
                            total: controller.tutorData.length,
                            child: Text(
                              'mark_all_as_read_str'.tr,
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                fontWeight:
                                    controller.isReadAll.isTrue
                                        ? FontWeight.w700
                                        : null,
                                color:
                                    controller.isReadAll.isTrue
                                        ? kColorGlobalBlue
                                        : Colors.grey,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: paddingExtraLarge),
                    Obx(() {
                      if (controller.arrData.isEmpty) {
                        return EmptyStateView();
                      }
                      return Column(
                        children: [
                          for (int i = 0; i < controller.arrData.length; i++)
                            Column(
                              children: [
                                _items(context, controller.arrData[i]),
                              ],
                            ),
                        ],
                      );
                    }),

                    if (controller.isLoading.value)
                      Center(child: CircularProgressIndicator()),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _items(context, InboxModel data) {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          Routes.NOTIFICATION_DETAIL_PAGE,
          arguments: {'id': data.id},
        );
      },
      child: Dismissible(
        key: Key(data.id.toString()),
        direction: DismissDirection.endToStart,
        background: Container(
          decoration: BoxDecoration(color: Color.fromRGBO(12, 157, 235, 1)),
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-notes.svg',
            color: Colors.white,
          ),
        ),
        secondaryBackground: Container(
          decoration: BoxDecoration(color: Color.fromRGBO(175, 5, 25, 1)),
          alignment: Alignment.centerRight,
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-trash-bin.svg',
            color: Colors.white,
          ),
        ),
        onDismissed: (direction) {
          if (direction == DismissDirection.endToStart) {
            controller.deleteNotification(data.id ?? 0);
          }
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                color:
                    data.isRead == true ? Colors.transparent : kColorGlobalBlue,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          data.title ?? '',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            fontWeight:
                                data.isRead == false ? FontWeight.w700 : null,
                          ),
                        ),
                        Text(
                          ' - ${Utils.formatCustomDate(data.createdAt ?? '')}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: kColorTextTersierLight),
                        ),
                      ],
                    ),
                    SizedBox(height: paddingSmall),
                    Text(
                      data.body ?? '',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: kColorTextTersierLight,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsetsGeometry.symmetric(
                        vertical: paddingSmall,
                      ),
                      child: Divider(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

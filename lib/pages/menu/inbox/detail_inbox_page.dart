import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/inbox/detail_inbox_controller.dart';
import 'package:pdl_superapp/controllers/inbox/inbox_list_controller.dart';
import 'package:pdl_superapp/pages/menu/inbox/inbox_widgets/shimmer_detail_inbox.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class DetailInboxPage extends StatefulWidget {
  const DetailInboxPage({super.key});

  @override
  State<DetailInboxPage> createState() => _DetailInboxPageState();
}

class _DetailInboxPageState extends State<DetailInboxPage> {
  final controller = Get.put(DetailInboxController());
  InboxListController listController = Get.find();

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,

      body: Stack(
        children: [
          Utils.cachedImageWrapper(
            'image/img-login-header.png',
            alignment: Alignment.topCenter,
          ),
          Container(
            width: Get.width,
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            decoration: BoxDecoration(
              color: Get.isDarkMode ? kColorBgDark : kColorBgLight,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            margin: EdgeInsets.only(top: 40),
            child: Column(
              children: [
                _AppBar(
                  onArchive: () {
                    controller.reqBulkArchiveInbox();
                  },
                  onRead: () async {
                    await controller.reqReadInbox();
                  },
                  onDelete: () async {
                    await controller.reqBulkDeleteInbox();
                  },
                ),
                Obx(() {
                  if (controller.isLoading.value) {
                    return ShimmerDetailInbox();
                  }

                  DateTime createAtDateTime = DateTime.now();
                  if (controller.detailInboxResp.value?.createdAt != null &&
                      controller.detailInboxResp.value!.createdAt!.isNotEmpty) {
                    createAtDateTime = DateTime.parse(
                      controller.detailInboxResp.value!.createdAt!,
                    );
                  } else {}

                  return ListView(
                    shrinkWrap: true,
                    padding: EdgeInsets.only(top: paddingMedium),
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              DateFormat(
                                'dd MMM yyyy HH:mm',
                              ).format(createAtDateTime),
                              textAlign: TextAlign.right,
                              style: textTheme.bodyMedium,
                            ),
                          ),
                          SizedBox(height: paddingSmall),
                          Text(
                            controller.detailInboxResp.value?.title ?? '',
                            style: textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: paddingSmall),
                          Text(
                            '${controller.detailInboxResp.value?.body}',
                            style: textTheme.bodyMedium,
                          ),

                          Container(
                            margin: EdgeInsets.symmetric(
                              vertical: paddingMedium,
                            ),
                            width: Get.width,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: kLine),
                            ),
                            child: Column(
                              children: [
                                Container(
                                  width: Get.width,
                                  padding: EdgeInsets.all(paddingSmall),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10),
                                      topRight: Radius.circular(10),
                                    ),
                                    color: kLine,
                                  ),
                                  child: Text(
                                    'title_agency_information'.tr,
                                    style: textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(paddingSmall),
                                  child: Column(
                                    children: [
                                      _titleValue(
                                        context,
                                        title: 'label_form_agent_code'.tr,
                                        value:
                                            controller.agent.value?.agentCode ??
                                            '',
                                      ),
                                      _titleValue(
                                        context,
                                        title: 'label_form_full_name'.tr,
                                        value:
                                            controller.agent.value?.name ?? '',
                                      ),
                                      _titleValue(
                                        context,
                                        title: 'label_current_level'.tr,
                                        value:
                                            controller
                                                .agent
                                                .value
                                                ?.agentLevel ??
                                            '',
                                      ),
                                      _titleValue(
                                        context,
                                        title: 'promotion_level_str'.tr,
                                        value:
                                            controller
                                                .detailInboxResp
                                                .value
                                                ?.detailData
                                                ?.positionLevel ??
                                            '',
                                      ),
                                      PdlButton(
                                        width: Get.width,
                                        onPressed: () {
                                          if (controller
                                              .detailInboxResp
                                              .value!
                                              .trxType!
                                              .contains('RECRUITMENT')) {
                                            Get.toNamed(
                                              Routes.APPROVAL,
                                              arguments: {
                                                'uuid':
                                                    controller
                                                        .detailInboxResp
                                                        .value
                                                        ?.detailData
                                                        ?.uuid,
                                              },
                                            );
                                          } else if (controller
                                              .detailInboxResp
                                              .value!
                                              .trxType!
                                              .contains('REJOIN')) {
                                            Get.toNamed(
                                              Routes.DETAIL_REJOIN,
                                              arguments: {
                                                'id_rejoin':
                                                    controller
                                                        .detailInboxResp
                                                        .value
                                                        ?.trxId,
                                                'id_approval_header':
                                                    controller
                                                        .detailInboxResp
                                                        .value
                                                        ?.detailData
                                                        ?.approvalHeader
                                                        ?.id,
                                              },
                                            );
                                          }
                                        },
                                        title:
                                            'view_submission_document_str'.tr,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: paddingSmall),
                          if (controller.detailInboxResp.value?.footer != null)
                            Text(
                              '${controller.detailInboxResp.value?.footer}',
                              style: textTheme.bodyMedium,
                            ),
                        ],
                      ),
                    ],
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Container _titleValue(
    BuildContext context, {
    required String title,
    required String value,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: paddingMedium),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(
            flex: 1,
            child: Text(':', style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(
            flex: 4,
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }
}

class _AppBar extends StatelessWidget {
  const _AppBar({
    required this.onArchive,
    required this.onRead,
    required this.onDelete,
  });
  final VoidCallback onArchive, onRead, onDelete;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 56,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () => Navigator.of(context).pop(),
            child: Icon(Icons.arrow_back),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                onTap: onArchive,
                child: Utils.cachedSvgWrapper(
                  width: 25,
                  'icon/ic-linear-notes.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
              SizedBox(width: paddingMedium),
              InkWell(
                onTap: onRead,
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-letter -unread.svg',
                  width: 25,
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
              SizedBox(width: paddingMedium),
              InkWell(
                onTap: onDelete,
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-trash-bin.svg',
                  width: 25,
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

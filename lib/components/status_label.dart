// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';

enum StatusApproval {
  BARU,
  TERTUNDA,
  MENUNGGU_PERSETUJUAN,
  DISETUJUI,
  DITOLAK,
  DIBATALKAN,
}

enum Status {
  DRAFT,
  IN_PROGRESS,
  COMPLETE,
  EXPIRED,
  REJECTED,
  CANCELLED,
  DIKEMBALIKAN,
}

class StatusLabel extends StatelessWidget {
  const StatusLabel({
    super.key,
    required this.status,
    this.isCancelable = true,
  });
  final String status;
  final bool isCancelable;

  Status? _parseStatus(String statusStr) {
    try {
      return Status.values.firstWhere((status) => status.name == statusStr);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    Color bgColor, textColor;
    String text = '';

    final statusEnum = _parseStatus(status);

    switch (statusEnum) {
      case Status.COMPLETE:
        bgColor = kColorGlobalBgGreen;
        textColor = kColorGlobalGreen;
        text = 'Disetujui';
        break;
      case Status.CANCELLED:
        bgColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        text = 'Dibatalkan';
      case Status.REJECTED:
        bgColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        text = 'Ditolak';
        break;
      case Status.EXPIRED:
        bgColor = kLine;
        textColor = kColorTextLight;
        text = 'Kadaluarsa';
        break;
      case Status.IN_PROGRESS:
        if (isCancelable) {
          bgColor = kColorGlobalBgBlue;
          textColor = kColorPaninBlue;
        } else {
          bgColor = kColorGlobalBgWarning;
          textColor = kColorGlobalWarning;
        }

        text = 'Diproses';
        break;
      default:
        bgColor = kColorGlobalBgBlue;
        textColor = kColorPaninBlue;
        text = status;
    }

    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(paddingExtraSmall),
      child: Text(
        text.toCapitalize(),
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: textColor),
      ),
    );
  }
}

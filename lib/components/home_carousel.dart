import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/widget/home_carousel_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class HomeCarousel extends StatelessWidget {
  final HomeCarouselController controller = Get.put(
    HomeCarouselController(),
    tag: Utils.getRandomString(),
  );

  HomeCarousel({super.key});

  // Softened opacity calculation for smoother transitions
  double calculateOpacity(int index, double currentPosition) {
    // For infinite scrolling, we need to normalize the position
    double normalizedPosition = currentPosition % controller.realItemCount;

    // Calculate the real index
    int realIndex = index % controller.realItemCount;

    // Account for wrapping around (when transitioning from last to first slide)
    double distance = min(
      (normalizedPosition - realIndex).abs(),
      min(
        (normalizedPosition - realIndex - controller.realItemCount).abs(),
        (normalizedPosition - realIndex + controller.realItemCount).abs(),
      ),
    );

    // Use a cosine curve for the smoothest possible transition
    // This gives a very soft fade in/out effect
    if (distance >= 1.0) {
      return 0.0;
    } else {
      // Cosine curve: 1 at distance=0, 0 at distance=1
      return (cos(distance * pi) + 1) / 2;
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return SizedBox(
      width: Get.width,
      child: AspectRatio(
        aspectRatio: 39 / 31,
        child: Stack(
          children: [
            // Background images with synchronized opacity
            Obx(
              () => Stack(
                children: List.generate(controller.realItemCount, (index) {
                  // Calculate normalized page position for infinite scrolling
                  final normalizedPage =
                      controller.currentPage.value % controller.realItemCount;

                  final opacity = calculateOpacity(index, normalizedPage);

                  return Opacity(
                    // Direct opacity instead of AnimatedOpacity to perfectly sync with scroll
                    opacity: opacity,
                    child: CachedNetworkImage(
                      imageUrl: controller.imageUrls[index],
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  );
                }),
              ),
            ),

            // Foreground Text Slider with PageView
            GestureDetector(
              onTapDown: (_) => controller.pauseAutoplay(),
              onTapUp: (_) => controller.resumeAutoplay(),
              onHorizontalDragStart: (_) => controller.pauseAutoplay(),
              onHorizontalDragEnd: (_) => controller.resumeAutoplay(),
              child: PageView.builder(
                controller: controller.pageController,
                itemCount:
                    controller
                        .displayedItemCount, // Large count for "infinite" scrolling
                physics: BouncingScrollPhysics(), // Softer scrolling physics
                itemBuilder: (context, index) {
                  // Get real index from the displayed index
                  final realIndex = controller.getRealIndex(index);
                  if (realIndex == -1) {
                    return Container();
                  }
                  return CachedNetworkImage(
                    imageUrl: controller.textUrls[realIndex],
                    fit: BoxFit.cover,
                  );
                },
              ),
            ),

            Positioned(
              bottom: isWideScreen ? (Get.height * 0.18) : 90,
              left: 0,
              right: 0,
              child: Center(
                child: Obx(
                  () => AnimatedSmoothIndicator(
                    activeIndex: controller.currentPage.round(),
                    count:
                        controller.imageUrls.isEmpty
                            ? 1
                            : controller.imageUrls.length,
                    effect: ExpandingDotsEffect(
                      dotColor: kLine,
                      activeDotColor: Colors.white,
                      dotHeight: 8,
                      dotWidth: 8,
                      spacing: 4,
                      expansionFactor: 4,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

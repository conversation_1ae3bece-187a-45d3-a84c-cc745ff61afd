import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/pages/error_page.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/dismiss_keyboard.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/languages/languages.dart';
import 'package:pdl_superapp/utils/themes/theme_primary.dart';
import 'package:pdl_superapp/utils/themes/theme_services.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'flavors.dart';

class App extends StatelessWidget {
  const App({super.key, required this.prefs});

  final SharedPreferences prefs;

  @override
  Widget build(BuildContext context) {
    // Untuk mendukung direct URL access, jangan set initialRoute
    // Biarkan GetX menggunakan URL yang diakses langsung
    // Middleware akan menangani redirect jika diperlukan

    Locale locals =
        prefs.getString(kFireDocLanguage) == 'en_US'
            ? Locale('en', 'US')
            : Locale('id', 'ID');
    return DismissKeyboard(
      child: GetMaterialApp(
        title: F.title,
        theme: ThemePrimary.light,
        darkTheme: ThemePrimary.dark,
        themeMode: ThemeServices.instance.themeMode,
        defaultTransition: Transition.noTransition,
        debugShowCheckedModeBanner: false,
        translations: Languages(),
        locale: locals,
        fallbackLocale: Languages.fallBackLocale,
        // Jangan set initialRoute, biarkan GetX handle URL routing secara natural
        unknownRoute: GetPage(
          name: Routes.NOT_FOUND,
          page: () => ErrorPage(message: 'Page Not Found'),
        ),
        getPages: AppPages.routes,
      ),
    );
  }
}

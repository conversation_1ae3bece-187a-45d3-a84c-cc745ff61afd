import 'dart:developer';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/models/branch_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class RecruitmentListController extends BaseControllers {
  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // SharedPreferences
  late SharedPreferences prefs;

  // List untuk menyimpan form dengan status draft
  RxList<RecruitmentFormModel> draftForms = <RecruitmentFormModel>[].obs;

  // List untuk menyimpan data dari API recruitment list
  RxList<RecruitmentApiModel> apiRecruitmentList = <RecruitmentApiModel>[].obs;

  RxString agentName = ''.obs;

  // Status loading
  RxBool isLoadingForms = false.obs;
  RxBool isLoadingApiData = false.obs;

  // Filter pencarian
  RxString searchQuery = ''.obs;

  // Branch functionality
  final candidateBranchController = TextEditingController();
  final candidateBranchCode = RxInt(0);
  RxList<BranchModels> branchList = RxList();
  RxList<BranchModels> allBranchList =
      RxList(); // Store all branch data for offline filtering
  RxBool isAgentLoading = false.obs;

  RxString selectedLevel = ''.obs;
  RxString candidateBranchText =
      ''.obs; // Reactive version of text for UI updates
  List<String> roleCandidate = ['BP', 'BM', 'BD'];

  // pagination
  int page = 0;
  int maxPage = 0;
  int limit = 10;
  bool enableLoadMore = true;
  bool? nextPage;
  bool? prevPage;
  ScrollController scrollController = ScrollController();

  RxString candidateBranchError = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    String recruiterLevel = prefs.getString(kStorageUserLevel) ?? '';
    agentName.value = prefs.getString(kStorageAgentName) ?? '';
    switch (recruiterLevel) {
      case kLevelBP:
        roleCandidate.retainWhere((item) => item == 'BP');
        break;
      case kLevelBM:
        roleCandidate.retainWhere((item) => item == 'BP' || item == 'BM');
        break;
      default:
    }
    setupScrollListener();
    startRealtimeListener();
    fetchApiRecruitmentList();

    // Initialize branch data for offline mode - fetch all branches
    _initializeBranchData();

    // Setup listener for candidateBranchController to sync with reactive variable
    candidateBranchController.addListener(() {
      candidateBranchText.value = candidateBranchController.text;
    });
  }

  @override
  void onClose() {
    // Hentikan real-time listener saat controller dihancurkan
    _firestoreService.dispose();
    candidateBranchController.dispose();
    super.onClose();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels == 0) {
          // You're at the top.
        } else {
          // You're at the bottom.
          loadMore();
        }
      }
    });
  }

  Future<void> loadMore() async {
    if (!enableLoadMore) return;
    if (page != (maxPage - 1)) {
      fetchApiRecruitmentList();
    }
  }

  // Memulai real-time listener untuk form draft
  Future<void> startRealtimeListener() async {
    isLoadingForms.value = true;

    try {
      await _firestoreService.startListeningToForms(
        onChanged: (List<RecruitmentFormModel> forms) {
          // Handle conflict resolution untuk real-time updates
          _handleRealtimeUpdate(forms);
          isLoadingForms.value = false;

          try {
            Get.find<LoggerService>().log(
              'Real-time update: ${forms.length} draft forms received',
            );
          } catch (_) {
            log('Real-time update: ${forms.length} draft forms received');
          }
        },
      );
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error starting real-time listener: $e');
      } catch (_) {
        log('Error starting real-time listener: $e');
      }
      isLoadingForms.value = false;
    }
  }

  // Handle real-time update dengan conflict resolution
  void _handleRealtimeUpdate(List<RecruitmentFormModel> newForms) {
    try {
      final currentForms = List<RecruitmentFormModel>.from(draftForms);
      final updatedForms = <RecruitmentFormModel>[];

      // Process setiap form baru dari real-time update
      for (final newForm in newForms) {
        final existingFormIndex = currentForms.indexWhere(
          (form) => form.id == newForm.id,
        );

        if (existingFormIndex >= 0) {
          final existingForm = currentForms[existingFormIndex];

          // Cek apakah ada konflik berdasarkan timestamp dan device ID
          final shouldUpdate = _shouldUpdateForm(existingForm, newForm);

          if (shouldUpdate) {
            updatedForms.add(newForm);
            log('Updated form ${newForm.id} from real-time listener');
          } else {
            updatedForms.add(existingForm);
            log('Kept existing form ${existingForm.id} (newer or same device)');
          }
        } else {
          // Form baru, tambahkan langsung
          updatedForms.add(newForm);
          log('Added new form ${newForm.id} from real-time listener');
        }
      }

      // Update draftForms dengan data yang sudah di-resolve
      draftForms.assignAll(updatedForms);
    } catch (e) {
      log('Error handling real-time update: $e');
      // Fallback: gunakan data baru jika error
      draftForms.assignAll(newForms);
    }
  }

  // Tentukan apakah form harus diupdate berdasarkan conflict resolution
  bool _shouldUpdateForm(
    RecruitmentFormModel existing,
    RecruitmentFormModel newForm,
  ) {
    try {
      final existingTimestamp = existing.lastUpdated ?? 0;
      final newTimestamp = newForm.lastUpdated ?? 0;

      // Jika timestamp baru lebih baru, update
      if (newTimestamp > existingTimestamp) {
        return true;
      }

      // Jika timestamp sama, cek device ID (simplified check)
      if (newTimestamp == existingTimestamp) {
        final existingDeviceId = _getFormDeviceId(existing);
        final newDeviceId = _getFormDeviceId(newForm);

        // Jika dari device yang sama, jangan update untuk menghindari loop
        if (existingDeviceId == newDeviceId) {
          return false;
        }
      }

      return false; // Default: jangan update jika tidak yakin
    } catch (e) {
      log('Error in conflict resolution: $e');
      return true; // Fallback: update jika error
    }
  }

  // Helper untuk mendapatkan device ID dari form
  String _getFormDeviceId(RecruitmentFormModel form) {
    try {
      return form.deviceId ?? 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  // Mengambil form dengan status draft (fallback method)
  Future<void> fetchDraftForms() async {
    isLoadingForms.value = true;

    try {
      final forms = await _firestoreService.getRecruitmentFormsByStatus(
        'draft',
      );
      draftForms.value = forms;
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error fetching draft forms: $e');
      } catch (_) {
        log('Error fetching draft forms: $e');
      }
    } finally {
      isLoadingForms.value = false;
    }
  }

  String setupParams() {
    String params = '';
    params = 'page=$page&size=$limit';
    return params;
  }

  // Filter form berdasarkan nama dan exclude yang sudah ada di API
  List<RecruitmentFormModel> get filteredForms {
    // Dapatkan list UUID dari API recruitment untuk filtering
    final apiUuids =
        apiRecruitmentList
            .map((api) => api.uuid)
            .where((uuid) => uuid != null)
            .toSet();

    // Filter draft forms yang tidak memiliki UUID yang sama dengan API data
    List<RecruitmentFormModel> filteredDrafts =
        draftForms.where((form) {
          // Jika form sudah memiliki server UUID dan ID-nya ada di API, jangan tampilkan
          if (apiUuids.contains(form.id)) {
            return false;
          }
          return true;
        }).toList();

    // Apply search filter jika ada
    if (searchQuery.value.isNotEmpty) {
      filteredDrafts =
          filteredDrafts.where((form) {
            final name = form.namaKtp?.toLowerCase() ?? '';
            return name.contains(searchQuery.value.toLowerCase());
          }).toList();
    }

    // Sort berdasarkan lastUpdated (terbaru dulu)
    filteredDrafts.sort((a, b) {
      final aTime = a.lastUpdated ?? 0;
      final bTime = b.lastUpdated ?? 0;
      return bTime.compareTo(aTime); // DESC order (terbaru dulu)
    });

    return filteredDrafts;
  }

  // Update filter pencarian
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  // Mengambil data recruitment dari API
  Future<void> fetchApiRecruitmentList() async {
    isLoadingApiData.value = true;
    try {
      String params = setupParams();
      await api.getRecruitmentList(
        controllers: this,
        params: params,
        code: kReqGetRecruitmentList,
      );
    } catch (e) {
      try {
        Get.find<LoggerService>().log(
          'Error fetching API recruitment list: $e',
        );
      } catch (_) {
        log('Error fetching API recruitment list: $e');
      }
    }
    // Loading akan di-set false di loadSuccess atau loadFailed
  }

  // Handle API response
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetRecruitmentList:
        parsePagination(response);
        parseApiRecruitmentList(response);
        break;
      case kReqGetBranch:
        parseDataBranch(response);
        break;
      case kReqGetAllBranch:
        parseAllBranchData(response);
        break;
      default:
        break;
    }
  }

  @override
  loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    isLoadingApiData.value = false;
  }

  parsePagination(response) {
    if (response['last'] == false) {
      page = page + 1;
    } else {
      enableLoadMore = response['last'] == false;
    }
    maxPage = response['totalPages'];
  }

  // Parse API recruitment list response
  void parseApiRecruitmentList(dynamic response) {
    try {
      if (response['first'] == true) {
        apiRecruitmentList.clear();
      }

      // Handle different response structures
      var data = [];
      if (response is Map && response.containsKey('content')) {
        data = response['content'] as List;
      } else if (response is List) {
        data = response;
      }

      for (var item in data) {
        try {
          RecruitmentApiModel recruitment = RecruitmentApiModel.fromJson(item);
          apiRecruitmentList.add(recruitment);
        } catch (e) {
          log('Error parsing recruitment item: $e');
        }
      }

      log(
        'Successfully loaded ${apiRecruitmentList.length} recruitment items from API',
      );
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error parsing API recruitment list: $e');
      } catch (_) {
        log('Error parsing API recruitment list: $e');
      }
    } finally {
      isLoadingApiData.value = false;
    }
  }

  // Filter API recruitment berdasarkan nama
  List<RecruitmentApiModel> get filteredApiRecruitment {
    List<RecruitmentApiModel> filteredList = apiRecruitmentList;

    // Apply search filter jika ada
    if (searchQuery.value.isNotEmpty) {
      filteredList =
          filteredList.where((recruitment) {
            final name = recruitment.fullName?.toLowerCase() ?? '';
            return name.contains(searchQuery.value.toLowerCase());
          }).toList();
    }

    // Sort berdasarkan createdAt (terbaru dulu)
    filteredList.sort((a, b) {
      final aCreatedAt = a.createdAt;
      final bCreatedAt = b.createdAt;

      // Handle null values - put them at the end
      if (aCreatedAt == null && bCreatedAt == null) return 0;
      if (aCreatedAt == null) return 1;
      if (bCreatedAt == null) return -1;

      try {
        final aTime = DateTime.parse(aCreatedAt);
        final bTime = DateTime.parse(bCreatedAt);
        return bTime.compareTo(aTime); // DESC order (terbaru dulu)
      } catch (e) {
        // Jika parsing gagal, fallback ke string comparison
        return bCreatedAt.compareTo(aCreatedAt);
      }
    });

    return filteredList;
  }

  // Refresh data (untuk pull-to-refresh)
  Future<void> refreshData() async {
    page = 0;
    enableLoadMore = true;
    // Real-time listener akan otomatis mengupdate draft forms
    // Hanya perlu refresh API data
    await fetchApiRecruitmentList();
  }

  // Navigasi ke halaman form untuk melanjutkan pengisian
  void continueForm(String formId) async {
    await Get.toNamed(Routes.KEAGENAN_FORM, parameters: {'formId': formId});
    // Always refresh data when returning from form
    await refreshData();
  }

  // Navigasi ke halaman form baru
  void createNewForm() async {
    await Get.toNamed(Routes.KEAGENAN_FORM);
    // Always refresh data when returning from form
    await refreshData();
  }

  // Branch functionality methods
  void parseDataBranch(response) {
    final branches =
        (response['content'] as List)
            .map((item) => BranchModels.fromJson(item))
            .toList();

    branchList.clear();
    branchList.assignAll(branches);
    isAgentLoading.value = false;
    log('Branch list loaded: ${branchList.length} items');
  }

  void parseAllBranchData(response) {
    final branches =
        (response['content'] as List)
            .map((item) => BranchModels.fromJson(item))
            .toList();

    allBranchList.clear();
    allBranchList.assignAll(branches);

    // Don't populate the display list initially - keep it empty until user types
    branchList.clear();

    isAgentLoading.value = false;
    log('All branch data loaded: ${allBranchList.length} items');
  }

  // Initialize branch data for offline mode - fetch all branches
  void _initializeBranchData() {
    isAgentLoading.value = true;
    // Fetch all branches with empty branchName parameter
    api.getBranch(
      controllers: this,
      code: kReqGetAllBranch,
      params: "branchName=&size=999",
    );
  }

  // Branch onTextUpdate - now filters locally instead of making API calls
  void onBranchTextChanged(String value) {
    Future.delayed(Duration(milliseconds: 200)).then((val) {
      if (value.isNotEmpty) {
        // Filter from local data instead of making API call
        _filterBranchesLocally(value);
      } else {
        // Clear branch list when search is empty (hide dropdown)
        branchList.clear();
        // Reset branch code when text is cleared
        candidateBranchCode.value = 0;
      }
    });
  }

  // Filter branches locally based on search query
  void _filterBranchesLocally(String query) {
    if (allBranchList.isEmpty) {
      // If no data loaded yet, try to initialize
      _initializeBranchData();
      return;
    }

    final filteredBranches =
        allBranchList.where((branch) {
          final branchName = branch.branchName?.toLowerCase() ?? '';
          return branchName.contains(query.toLowerCase());
        }).toList();

    branchList.clear();
    branchList.assignAll(filteredBranches);

    // Check if current selected branch is still valid after filtering
    _validateCurrentBranchSelection(query, filteredBranches);

    log('Filtered branches: ${branchList.length} items for query: $query');
  }

  // Validate if current branch selection is still valid after filtering
  void _validateCurrentBranchSelection(
    String query,
    List<BranchModels> filteredBranches,
  ) {
    // If we have a current branch code and the text matches exactly with a branch name
    if (candidateBranchCode.value != 0 &&
        candidateBranchController.text.isNotEmpty) {
      // Find if the current branch text exactly matches any branch in filtered results
      BranchModels? exactMatch;
      try {
        exactMatch = filteredBranches.firstWhere(
          (branch) =>
              branch.branchName?.toLowerCase() ==
              candidateBranchController.text.toLowerCase(),
        );
      } catch (e) {
        exactMatch = null;
      }

      // If exact match found and it matches the current branch code, keep it
      if (exactMatch != null && exactMatch.id == candidateBranchCode.value) {
        // Current selection is still valid, no need to reset
        return;
      }

      // If no exact match or different branch, reset the branch code
      if (exactMatch == null ||
          candidateBranchController.text.toLowerCase() != query.toLowerCase()) {
        candidateBranchCode.value = 0;
        log('Branch code reset due to text change: $query');
      }
    }
  }
}

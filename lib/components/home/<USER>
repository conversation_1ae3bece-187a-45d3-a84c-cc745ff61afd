import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/utils/keys.dart';

class NotificationDetailController extends BaseControllers {
  String id = '0';
  Rx<InboxModel> data = InboxModel().obs;

  @override
  void onInit() {
    super.onInit();
    id = (Get.arguments['id'] ?? '').toString();
    load();
  }

  @override
  void load() {
    super.load();
    api.getDetailInbox(controllers: this, id: id, code: kReqDetailInbox);
    api.postReadInbox(controllers: this, code: kReqReadInbox, ids: [id]);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqDetailInbox:
        data.value = InboxModel.fromJson(response);
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }
}

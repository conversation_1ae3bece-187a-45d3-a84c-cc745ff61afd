import 'dart:typed_data';
import 'package:flutter/material.dart';

void useWebFeature() {}

Future<Uint8List?> blobUrlToBytes(String blobUrl) async {
  return null;
}

// WebView stub functions for non-web platforms
String createWebViewIframe(String url, {Function(bool)? onScrolledToBottom}) {
  // Return empty string for non-web platforms
  return '';
}

Widget createWebViewWidget(String? iframeViewType) {
  // Return empty container for non-web platforms
  return const SizedBox.shrink();
}

// Tesseract OCR stub function for non-web platforms
Future<String?> performTesseractOCR(String base64Image) async {
  // Return null for non-web platforms (mobile uses different OCR)
  return null;
}

import 'dart:async';
import 'dart:developer';
import 'dart:io' show File, Directory;
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';

class PdlPHotoController extends BaseControllers {
  // Camera controller
  Rx<CameraController?> cameraController = Rx<CameraController?>(null);
  RxBool isCameraInitialized = false.obs;
  RxBool isTakingPicture = false.obs;
  Rx<File?> capturedImage = Rx<File?>(null);
  String type = '';

  // Camera switching
  RxList<CameraDescription> availableCamerasList = RxList();
  RxInt currentCameraIndex = 0.obs;
  RxBool isSwitchingCamera = false.obs;

  // Camera permission handling
  RxBool cameraPermissionDenied = false.obs;

  @override
  void onClose() {
    // Use resetCameraState for safe disposal
    resetCameraState();
    super.onClose();
  }

  /// Reset camera state - useful when returning to photo pages
  void resetCameraState() {
    // Cancel any pending debounce timer
    _debounceTimer?.cancel();
    _debounceTimer = null;

    // Stop any ongoing initialization
    isInitializing.value = false;

    cameraPermissionDenied.value = false;
    isCameraInitialized.value = false;
    isTakingPicture.value = false;
    isSwitchingCamera.value = false;

    // Dispose camera controller safely
    try {
      cameraController.value?.dispose();
    } catch (e) {
      log('Error disposing camera controller: $e');
    } finally {
      cameraController.value = null;
    }

    // Clear camera list
    availableCamerasList.clear();
    currentCameraIndex.value = 0;

    // Reset web retry counter
    _webRetryCount = 0;
  }

  /// Filter cameras to only include main front and back cameras
  /// Returns cameras in order: back camera first, then front camera
  List<CameraDescription> _filterMainCameras(List<CameraDescription> cameras) {
    final List<CameraDescription> mainCameras = [];

    // First, add back camera
    final backCamera = cameras.firstWhereOrNull(
      (camera) => camera.lensDirection == CameraLensDirection.back,
    );
    if (backCamera != null) {
      mainCameras.add(backCamera);
    }

    // Then, add front camera
    final frontCamera = cameras.firstWhereOrNull(
      (camera) => camera.lensDirection == CameraLensDirection.front,
    );
    if (frontCamera != null) {
      mainCameras.add(frontCamera);
    }

    return mainCameras;
  }

  /// Get user-friendly camera name for debugging
  String _getCameraName(CameraDescription camera) {
    switch (camera.lensDirection) {
      case CameraLensDirection.front:
        return 'Front Camera';
      case CameraLensDirection.back:
        return 'Back Camera';
      case CameraLensDirection.external:
        return 'External Camera';
    }
  }

  // Add initialization guard to prevent multiple simultaneous initializations
  RxBool isInitializing = false.obs;

  // Add debounce timer for web to prevent rapid state changes
  Timer? _debounceTimer;

  // Web-specific retry mechanism
  int _webRetryCount = 0;
  static const int _maxWebRetries = 3;

  Future<bool> initializeCamera() async {
    log('🎥 Starting camera initialization...');

    // Prevent multiple simultaneous initialization attempts
    if (isInitializing.value) {
      log('⚠️ Camera initialization already in progress, skipping...');
      return false;
    }

    // If camera is already initialized, return true
    if (isCameraInitialized.value && cameraController.value != null) {
      log('✅ Camera already initialized, skipping...');
      return true;
    }

    return await _initializeCameraWithRetry();
  }

  Future<bool> _initializeCameraWithRetry() async {
    try {
      isInitializing.value = true;
      log('🔄 Setting isInitializing to true');

      // Add longer delay for web to allow browser permission dialog to settle
      if (kIsWeb) {
        log(
          '🌐 Adding web delay (attempt ${_webRetryCount + 1}/$_maxWebRetries)...',
        );
        // Increase delay based on retry count to give more time for permission dialog
        final delayMs = 500 + (_webRetryCount * 1000); // 500ms, 1.5s, 2.5s
        await Future.delayed(Duration(milliseconds: delayMs));
      }

      // Reset permission denied flag
      cameraPermissionDenied.value = false;
      log('🔓 Reset permission denied flag');

      // Get available cameras
      log('📷 Getting available cameras...');
      final cameras = await availableCameras();
      log('📷 Found ${cameras.length} cameras');

      // Log camera details for web debugging
      if (kIsWeb && cameras.isNotEmpty) {
        for (int i = 0; i < cameras.length; i++) {
          final camera = cameras[i];
          log('📷 Camera $i: ${camera.name} (${camera.lensDirection})');
        }
      }

      if (cameras.isEmpty) {
        log('❌ No cameras available');
        Get.snackbar(
          'Error',
          'No cameras available',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }

      // Filter to only include front and back cameras
      availableCamerasList.value = _filterMainCameras(cameras);
      log('🔍 Filtered to ${availableCamerasList.length} main cameras');

      // If no front/back cameras found, use all cameras as fallback
      if (availableCamerasList.isEmpty) {
        log('⚠️ No main cameras found, using all cameras as fallback');
        availableCamerasList.value = cameras;
      }

      currentCameraIndex.value =
          0; // Start with first camera (usually back camera)

      // Use the first camera (usually the back camera)
      final camera = availableCamerasList[currentCameraIndex.value];
      log('🎯 Initializing ${_getCameraName(camera)}');

      // Initialize the camera controller with optimized settings
      log('⚙️ Creating camera controller...');
      final controller = CameraController(
        camera,
        // Use medium resolution for web to get better quality than low but avoid performance issues
        kIsWeb ? ResolutionPreset.medium : ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // Initialize the controller
      log('🔄 Initializing camera controller...');
      await controller.initialize();
      log('✅ Camera controller initialized successfully');

      // Update the controller
      cameraController.value = controller;
      isCameraInitialized.value = true;
      log('✅ Camera initialization completed successfully');
      return true;
    } catch (e) {
      log('❌ Camera initialization error: $e');

      // Check if the error is related to camera permission
      String errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains('permission') ||
          errorMessage.contains('denied') ||
          errorMessage.contains('access') ||
          errorMessage.contains('camera')) {
        log('🚫 Camera permission denied');

        // For web, implement retry mechanism for permission issues
        if (kIsWeb && _webRetryCount < _maxWebRetries) {
          _webRetryCount++;
          log(
            '🔄 Web retry attempt $_webRetryCount/$_maxWebRetries for permission issue',
          );
          isInitializing.value = false;

          // Wait longer before retry to allow user to interact with permission dialog
          await Future.delayed(const Duration(seconds: 3));
          return await _initializeCameraWithRetry();
        }

        cameraPermissionDenied.value = true;
        Get.snackbar(
          'Permission Denied',
          kIsWeb
              ? 'Camera permission is required. Please click "Allow" when your browser asks for camera access, then try again.'
              : 'Camera permission is required to take photos. Please allow camera access and try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 5), // Longer duration for web
        );
      } else {
        log('💥 Other camera error: $e');

        // For web, also retry on other camera errors (might be timing issues)
        if (kIsWeb && _webRetryCount < _maxWebRetries) {
          _webRetryCount++;
          log(
            '🔄 Web retry attempt $_webRetryCount/$_maxWebRetries for camera error',
          );
          isInitializing.value = false;

          // Wait before retry
          await Future.delayed(const Duration(seconds: 2));
          return await _initializeCameraWithRetry();
        }

        Get.snackbar(
          'Error',
          'Failed to initialize camera: $e',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
      return false;
    } finally {
      isInitializing.value = false;
      log('🔄 Set isInitializing to false');
    }
  }

  Future<void> switchCamera() async {
    if (availableCamerasList.length <= 1 ||
        isSwitchingCamera.value ||
        isInitializing.value) {
      return; // No other cameras available, already switching, or initializing
    }

    try {
      isSwitchingCamera.value = true;

      // Dispose current controller
      await cameraController.value?.dispose();
      cameraController.value = null;
      isCameraInitialized.value = false;

      // Switch to next camera
      currentCameraIndex.value =
          (currentCameraIndex.value + 1) % availableCamerasList.length;

      // Initialize new camera
      final camera = availableCamerasList[currentCameraIndex.value];
      log('Switching to ${_getCameraName(camera)}');

      final controller = CameraController(
        camera,
        // Use medium resolution for web - balance between quality and performance
        kIsWeb ? ResolutionPreset.medium : ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await controller.initialize();

      // Double-check that we haven't been disposed during switching
      if (isSwitchingCamera.value) {
        // Update the controller
        cameraController.value = controller;
        isCameraInitialized.value = true;
        log('Camera switched successfully');
      } else {
        // If we were disposed during switching, clean up the new controller
        await controller.dispose();
      }
    } catch (e) {
      log('Camera switch error: $e');
      Get.snackbar(
        'Error',
        'Failed to switch camera: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isSwitchingCamera.value = false;
    }
  }

  Future<void> takePicture() async {
    if (cameraController.value == null ||
        !isCameraInitialized.value ||
        isTakingPicture.value) {
      return;
    }

    try {
      isTakingPicture.value = true;

      // Take the picture
      final XFile photo = await cameraController.value!.takePicture();

      dynamic imageToProcess;

      if (kIsWeb) {
        // On web, use XFile directly
        imageToProcess = photo;
        capturedImage.value = null; // Can't store File on web
      } else {
        // On mobile, create File and save to permanent location
        final File imageFile = File(photo.path);
        final Directory appDir = await getApplicationDocumentsDirectory();
        final String fileName = path.basename(photo.path);
        final File savedImage = await imageFile.copy(
          '${appDir.path}/$fileName',
        );

        imageToProcess = savedImage;
        capturedImage.value = savedImage;
      }

      isTakingPicture.value = false;

      // Navigate to the image cropper page
      final croppedImage = await Get.toNamed(
        Routes.PHOTO_IMAGE_CROPPER,
        arguments: {'imageFile': imageToProcess, 'type': type},
      );

      // Dispose camera after taking picture (regardless of crop result)
      resetCameraState();

      // If we got a cropped image back, return it
      if (croppedImage != null) {
        Get.back(result: croppedImage);
      } else {
        // If user cancelled cropping, also go back
        Get.back();
      }
    } catch (e) {
      isTakingPicture.value = false;
      // Dispose camera on error as well
      resetCameraState();
      Get.snackbar(
        'Error',
        'Failed to take picture: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void toastError({required String title, required String message}) {
    Get.snackbar(
      title,
      message,
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  Future<dynamic> cropImage(dynamic imageFile) async {
    try {
      // check file size here first, if > 2mb then show error and back to previous page
      // Skip file size check on web as File.length() is not supported

      if (!kIsWeb && imageFile is File) {
        int fileSizeInBytes = await imageFile.length();
        double fileSizeInMB = fileSizeInBytes / (1024 * 1024);
        log('here $fileSizeInBytes');
        log('here mb $fileSizeInMB');
        if (fileSizeInMB > 2) {
          toastError(title: 'Error', message: 'photo_max_2mb'.tr);
          return null;
        }
      }

      log('here bro');

      // Navigate to the image cropper page
      final croppedImage = await Get.toNamed(
        Routes.PHOTO_IMAGE_CROPPER,
        arguments: {'imageFile': imageFile, 'type': type},
      );

      // Return the cropped image if available
      if (croppedImage != null) {
        return croppedImage;
      }

      return null;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to crop image: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }
  }
}

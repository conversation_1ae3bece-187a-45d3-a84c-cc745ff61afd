import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home_carousel.dart';
import 'package:pdl_superapp/components/pdl_showcase.dart';
import 'package:pdl_superapp/controllers/home_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shimmer/shimmer.dart';

import '../../controllers/home_widget/appbar_controller.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // ignore: prefer_function_declarations_over_variables
  final ScrollAppBarController _scrollAppBarController =
      (() {
        try {
          return Get.find<ScrollAppBarController>();
        } catch (_) {
          return Get.put(ScrollAppBarController());
        }
      })();

  final scrollController = ScrollController();
  final HomeController controller = Get.put(HomeController(), tag: 'home');

  @override
  void initState() {
    super.initState();
    scrollController.addListener(() {
      _scrollAppBarController.handleScroll(scrollController.offset);
    });
  }

  @override
  Widget build(BuildContext context) {
    return body();
  }

  RefreshIndicator body() {
    return RefreshIndicator(
      onRefresh: () async {
        if (!kIsWeb) {
          await Utils.checkForUpdates();
        }
        controller.load();
      },
      child: SingleChildScrollView(
        controller: scrollController,
        physics: AlwaysScrollableScrollPhysics(parent: ClampingScrollPhysics()),
        child: Stack(
          children: [
            SizedBox(
              width: Get.width,
              child: Utils.cachedImageWrapper(
                'image/img-bg-mobile.png',
                fit: BoxFit.cover,
              ),
            ),
            Container(width: Get.width),
            Stack(
              children: [
                // container for carousel
                HomeCarousel(),
                Column(
                  children: [
                    AspectRatio(aspectRatio: 39 / 24),
                    Stack(
                      children: [
                        Container(
                          width: Get.width,
                          margin: EdgeInsets.only(top: 50),
                          constraints: BoxConstraints(minHeight: 300),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                          ),
                        ),
                        Column(
                          children: [
                            _nameCard(context),
                            SizedBox(height: paddingLarge),
                            PdlShowcase(
                              globalKey: controller.mainC.tutorBeranda[3].key,
                              title: controller.mainC.tutorBeranda[3].title,
                              description:
                                  controller.mainC.tutorBeranda[3].description,
                              number: controller.mainC.tutorBeranda[3].number,
                              total: controller.mainC.tutorBeranda.length,
                              child: HomeMenu(),
                            ),
                            SizedBox(height: paddingMedium),
                            Divider(thickness: 6),
                            SizedBox(height: paddingMedium),
                            PdlShowcase(
                              globalKey: controller.mainC.tutorBeranda[4].key,
                              title: controller.mainC.tutorBeranda[4].title,
                              description:
                                  controller.mainC.tutorBeranda[4].description,
                              number: controller.mainC.tutorBeranda[4].number,
                              total: controller.mainC.tutorBeranda.length,
                              child: HomeTitle(
                                title: 'fave_widget_str'.tr,
                                actionWidget: GestureDetector(
                                  onTap: () => Get.toNamed(Routes.FAVORITE),
                                  child: Container(
                                    color: Colors.transparent,
                                    child: Wrap(
                                      spacing: paddingSmall,
                                      crossAxisAlignment:
                                          WrapCrossAlignment.center,
                                      children: [
                                        Utils.cachedSvgWrapper(
                                          'icon/ic-linear-filter-2.svg',
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                        ),
                                        Text(
                                          'change_str'.tr,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodyMedium?.copyWith(
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: paddingLarge),

                            // Dynamic favorite widgets
                            Obx(() {
                              if (controller.isLoading.isTrue) {
                                return Center(
                                  child: CircularProgressIndicator(),
                                );
                              }
                              return Column(
                                children: [
                                  for (
                                    int i = 0;
                                    i < controller.favoriteWidgets.length;
                                    i++
                                  )
                                    controller.favoriteWidgets[i],
                                ],
                              );
                            }),
                            // Obx(() {
                            //   if (controller.state.value ==
                            //       ControllerState.loading) {
                            //     return CircularProgressIndicator();
                            //   }
                            //   return ;
                            // }),

                            // SizedBox(height: paddingExtraLarge),

                            // PdlButton(
                            //   controller: controller,
                            //   title: 'Start Tutorial',
                            //   onPressed:
                            //       () => controller.startShowCase(context),
                            // ),
                            // Row(
                            //   mainAxisAlignment: MainAxisAlignment.center,
                            //   children: [
                            //     PdlShowcase(
                            //       globalKey: controller.keyThree,
                            //       child: Container(
                            //         width: 100,
                            //         height: 100,
                            //         decoration: BoxDecoration(
                            //           color: kColorError,
                            //           borderRadius: BorderRadius.circular(100),
                            //         ),
                            //       ),
                            //     ),
                            //     SizedBox(width: paddingMedium),
                            //     PdlShowcase(
                            //       globalKey: controller.keyOne,
                            //       radius: 100,
                            //       child: Container(
                            //         width: 100,
                            //         height: 100,
                            //         decoration: BoxDecoration(
                            //           color: kColorError,
                            //           borderRadius: BorderRadius.circular(100),
                            //         ),
                            //       ),
                            //     ),
                            //   ],
                            // ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Padding _nameCard(BuildContext context) {
    RxString agentName = '-'.obs;
    RxString agentLevel = '-'.obs;
    RxString agentInitials = ''.obs;
    String? photoProfile = controller.userData.value.photo;

    switch (controller.userLevel.value) {
      case kUserLevelBp:
      case kUserLevelBm:
      case kUserLevelBd:
        agentName.value = controller.userData.value.agentName ?? '-';
        agentLevel.value =
            '${controller.userData.value.level ?? '-'} - ${controller.userData.value.agentCode ?? '-'}';
        break;
      case kUserLevelCAO:
        agentName.value = controller.userData.value.name ?? '-';
        agentLevel.value = controller.userData.value.roles?.name ?? '-';
      default:
        // BDM up selain CAO
        agentName.value =
            controller.userData.value.name ??
            controller.userData.value.agentName ??
            '-';
        agentLevel.value = controller.userData.value.roles?.name ?? '-';
    }

    if (photoProfile != null && photoProfile != '') {
      agentInitials.value = '';
    } else {
      agentInitials.value = Utils.getInitials(agentName.value);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: paddingLarge),
      child: PdlShowcase(
        globalKey: controller.mainC.tutorBeranda[2].key,
        radius: 16,
        title: controller.mainC.tutorBeranda[2].title,
        description: controller.mainC.tutorBeranda[2].description,
        number: controller.mainC.tutorBeranda[2].number,
        total: controller.mainC.tutorBeranda.length,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              width: Get.width,
              padding: EdgeInsets.all(paddingMedium),
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.surface.withValues(alpha: 0.4),
                border: Border.all(color: Color(0x404B6980)),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.surface.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Row(
                    children: [
                      // Profile Image
                      Obx(() {
                        if (controller.isLoading.value) {
                          return Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          );
                        } else {
                          return SizedBox(
                            width: 40,
                            height: 40,
                            child: CircleAvatar(
                              radius: 30,
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              backgroundImage:
                                  photoProfile != null && photoProfile != ''
                                      ? CachedNetworkImageProvider(photoProfile)
                                      : null,
                              child: Obx(
                                () => Text(
                                  agentInitials.value,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ), // Replace with your image
                            ),
                          );
                        }
                      }),
                      SizedBox(width: 15),
                      // Text Information
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Obx(() {
                              if (controller.isLoading.value) {
                                return Shimmer.fromColors(
                                  baseColor: Colors.grey[300]!,
                                  highlightColor: Colors.grey[100]!,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        height: 16,
                                        width: 120,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Container(
                                        height: 16,
                                        width: 150,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              } else {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Obx(
                                      () => Text(
                                        "${"hi_str".tr}, $agentName",
                                        style: Theme.of(
                                          context,
                                        ).textTheme.labelMedium?.copyWith(
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 5),
                                    Obx(
                                      () => Text(
                                        controller.userLevel.value ==
                                                    kUserLevelBp ||
                                                controller.userLevel.value ==
                                                    kUserLevelBm ||
                                                controller.userLevel.value ==
                                                    kUserLevelBd
                                            ? '${controller.userData.value.level ?? '-'} - ${controller.userData.value.agentCode ?? '-'}'
                                            : controller
                                                    .userData
                                                    .value
                                                    .roles
                                                    ?.name ??
                                                '-',
                                        style: Theme.of(
                                          context,
                                        ).textTheme.labelMedium?.copyWith(
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/polis_lapse_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:pdl_superapp/routes/app_routes.dart';

import '../widget/polis_lapse_page.dart';

class PolisLapsedWidget extends StatelessWidget {
  PolisLapsedWidget({super.key});

  // Use GetX controller
  final PolisLapseController controller = Get.put(
    PolisLapseController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        spacing: paddingMedium,
        children: [
          // Show tabs for non-BP roles
          if (controller.level != kLevelBP) _buildTabSelector(context),

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector for non-BP roles
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 0
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 0
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 1
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 1
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    controller.level == kLevelBD ? 'Group' : 'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role, show only individu content
    if (controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildIndividuContent();
      } else {
        return _buildTeamContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPolicyLapsedData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(onPressed: onRetry, child: Text('retry_str'.tr)),
            ],
          ),
        );
      }

      if (controller.individuController.policyLapsedList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data polis lapse"),
          ),
        );
      }

      // Show only first 3 records
      final displayedPolicies =
          controller.individuController.policyLapsedList.length > 3
              ? controller.individuController.policyLapsedList.sublist(0, 3)
              : controller.individuController.policyLapsedList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedPolicies.length,
            padding: EdgeInsets.zero,
            separatorBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: paddingMedium),
                  child: const Divider(height: 1),
                ),
            itemBuilder: (context, index) {
              final policy = displayedPolicies[index];
              return PolicyHolderItem(
                policy: policy,
                formatDate: controller.formatDate,
                formatCurrency: controller.formatCurrency,
                showAgentInfo: controller.selectedSection.value == 1,
              );
            },
          ),

          Container(
            margin: EdgeInsets.only(top: paddingMedium),
            width: double.infinity,
            child: PdlButton(
              title: 'display_detail'.tr,
              onPressed: () {
                final agentCode =
                    controller.prefs.getString(kStorageAgentCode) ?? '';
                Get.toNamed(
                  '${Routes.POLIS_LAPSE}?agentCode=$agentCode&withDownline=0',
                );
              },
            ),
          ),

          // Lihat Detail and Refresh buttons
          TextButton(
            onPressed: onRetry,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.refresh),
                SizedBox(width: paddingSmall),
                Text('refresh_str'.tr),
              ],
            ),
          ),
        ],
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPolicyLapsedData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(onPressed: onRetry, child: Text('retry_str'.tr)),
            ],
          ),
        );
      }

      if (controller.teamController.policyLapsedList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data polis lapse"),
          ),
        );
      }

      // Show only first 3 records
      final displayedPolicies =
          controller.teamController.policyLapsedList.length > 3
              ? controller.teamController.policyLapsedList.sublist(0, 3)
              : controller.teamController.policyLapsedList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedPolicies.length,
            padding: EdgeInsets.zero,
            separatorBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: paddingMedium),
                  child: const Divider(height: 1, thickness: 3),
                ),
            itemBuilder: (context, index) {
              final policy = displayedPolicies[index];
              return PolicyHolderItem(
                policy: policy,
                formatDate: controller.formatDate,
                formatCurrency: controller.formatCurrency,
                showAgentInfo: controller.selectedSection.value == 1,
              );
            },
          ),

          // Lihat Detail and Refresh buttons
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(top: paddingMedium),
            padding: const EdgeInsets.symmetric(vertical: paddingMedium),
            child: PdlButton(
              title: 'display_detail'.tr,
              onPressed: () {
                final agentCode =
                    controller.prefs.getString(kStorageAgentCode) ?? '';
                Get.toNamed(
                  '${Routes.POLIS_LAPSE}?agentCode=$agentCode&withDownline=1',
                );
              },
            ),
          ),
          TextButton(
            onPressed: onRetry,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.refresh),
                SizedBox(width: paddingSmall),
                Text('refresh_str'.tr),
              ],
            ),
          ),
        ],
      );
    });
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yy').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
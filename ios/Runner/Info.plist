<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>$(BUNDLE_DISPLAY_NAME)</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(BUNDLE_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>NSFaceIDUsageDescription</key>
    <string>We use Face ID to secure your data.</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>NSCameraUsageDescription</key>
	  <string>QuantumX360 Would like to access camera for profile pictures</string>
    <key>NSPhotoLibraryUsageDescription</key>
	  <string>QuantumX360 Would like to access Galery for profile pictures</string>
    <key>NSUserNotificationsUsageDescription</key>
    <string>This app needs notification access to send you important updates and alerts.</string>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    <key>UIBackgroundModes</key>
    <array>
      <string>fetch</string>
      <string>remote-notification</string>
    </array>
    <key>FirebaseAppDelegateProxyEnabled</key>
    <false/>
    <!-- WebView and Network Configuration -->
    <key>NSAppTransportSecurity</key>
    <dict>
      <key>NSAllowsArbitraryLoads</key>
      <true/>
      <key>NSAllowsArbitraryLoadsInWebContent</key>
      <true/>
    </dict>
    <!-- WebKit Configuration -->
    <key>WKAppBoundDomains</key>
    <array>
      <string>sandbox-quantumx.panindai-ichilife.co.id</string>
    </array>

    <!-- URL Schemes for Deep Linking -->
    <key>CFBundleURLTypes</key>
    <array>
      <dict>
        <key>CFBundleURLName</key>
        <string>quantumx360.deeplink</string>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>quantumx360</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleURLName</key>
        <string>quantumx360.universallinks</string>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>https</string>
        </array>
      </dict>
    </array>

    <!-- Associated Domains for Universal Links -->
    <key>com.apple.developer.associated-domains</key>
    <array>
      <string>applinks:sandbox-quantumx.panindai-ichilife.co.id</string>
    </array>
  </dict>
</plist>

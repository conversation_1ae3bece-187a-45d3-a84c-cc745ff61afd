import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/utils/constants.dart';

class NotificationDetailPage extends StatelessWidget {
  NotificationDetailPage({super.key});

  final NotificationDetailController controller = Get.put(
    NotificationDetailController(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Kemba<PERSON>',
      controller: controller,
      onRefresh: () {},
      child: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(() => TitleWidget(title: controller.data.value.title ?? '-')),
            Sized<PERSON>ox(height: paddingMedium),
            Obx(() => Text(controller.data.value.body ?? '-')),
          ],
        ),
      ),
    );
  }
}

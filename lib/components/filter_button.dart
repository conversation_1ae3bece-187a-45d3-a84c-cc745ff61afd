import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class FilterButton extends StatelessWidget {
  final int notificationCount;
  final Widget content;
  final String? title;
  final bool dismissable;

  const FilterButton({
    super.key,
    this.notificationCount = 0,
    required this.content,
    this.dismissable = false,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          () => PdlBottomSheet(
            content: content,
            title: title ?? 'Filter',
            // noPadding: true,
            dismissable: dismissable,
          ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        height: 48,
        decoration: BoxDecoration(
          color:
              notificationCount > 0
                  ? Colors.blue.withValues(alpha: 0.2)
                  : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color:
                notificationCount > 0
                    ? Colors.blue
                    : (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Utils.cachedSvgWrapper(
                  'icon/ic-linear-Filter.svg',
                  color:
                      notificationCount > 0
                          ? kColorGlobalBlue
                          : (Get.isDarkMode ? kColorTextDark : kColorTextLight),
                ),
                if (notificationCount > 0)
                  Positioned(
                    top: -10,
                    right: -10,
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        '$notificationCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(width: 8),
            Text(
              'Filter',
              style: TextStyle(
                color:
                    notificationCount > 0
                        ? kColorGlobalBlue
                        : (Get.isDarkMode ? kColorTextDark : kColorTextLight),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/claim_tracking_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ClaimIndividuController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the claim tracking data
  RxList<ClaimTrackingModel> claimTrackingList = <ClaimTrackingModel>[].obs;

  // Error handling
  RxBool hasClaimError = false.obs;
  RxString claimErrorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchClaimTrackingData();
  }

  // Fetch claim tracking data from API
  Future<void> fetchClaimTrackingData() async {
    setLoading(true);
    hasClaimError.value = false;
    claimErrorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final agentCode = prefs.getString(kStorageAgentCode);

      if (agentCode != null) {
        await api.getStatusClaim(controllers: this, params: "agent_code=$agentCode&withDownline=0");
      } else {
        hasClaimError.value = true;
        claimErrorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);

    parseClaimTrackingData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasClaimError.value = true;
    claimErrorMessage.value = 'Failed to load claim tracking data';
    setLoading(false);
  }

  // Parse the claim tracking response data
  void parseClaimTrackingData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <ClaimTrackingModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(ClaimTrackingModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      claimTrackingList.assignAll(tempList);
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }
}

class ClaimTeamController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the claim tracking data
  RxList<ClaimTrackingModel> claimTrackingList = <ClaimTrackingModel>[].obs;

  // Error handling
  RxBool hasClaimError = false.obs;
  RxString claimErrorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchClaimTrackingData();
  }

  // Fetch claim tracking data from API
  Future<void> fetchClaimTrackingData() async {
    setLoading(true);
    hasClaimError.value = false;
    claimErrorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final agentCode = prefs.getString(kStorageAgentCode);

      if (agentCode != null) {
        // Use the same endpoint but with withDownline=1 parameter
        await api.getStatusClaim(controllers: this, params: "agent_code=$agentCode&withDownline=1");
      } else {
        hasClaimError.value = true;
        claimErrorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);

    parseClaimTrackingData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasClaimError.value = true;
    claimErrorMessage.value = 'Failed to load claim tracking data';
    setLoading(false);
  }

  // Parse the claim tracking response data
  void parseClaimTrackingData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <ClaimTrackingModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(ClaimTrackingModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      claimTrackingList.assignAll(tempList);
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }
}

class ClaimWidgetController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team/Group
  bool isShowOtherAgent = false;

  // Controllers for different tabs
  late ClaimIndividuController individuController = Get.put(ClaimIndividuController(), tag: Utils.getRandomString());

  late ClaimTeamController teamController = Get.put(ClaimTeamController(), tag: Utils.getRandomString());

  ClaimWidgetController({this.isShowOtherAgent = false}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    setLoading(true);
    refreshData();
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (level == kLevelBP || selectedSection.value == 0) {
      individuController.fetchClaimTrackingData();
    } else {
      teamController.fetchClaimTrackingData();
    }
    setLoading(false);
  }
}

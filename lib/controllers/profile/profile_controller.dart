// ignore_for_file: unused_field

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/tutor_model.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

class ProfileController extends BaseControllers {
  Rx<UserModels> userData = UserModels().obs;
  RxBool isBioActive = false.obs;

  final LocalAuthentication auth = LocalAuthentication();
  late SharedPreferences prefs;

  RxString userLevel = ''.obs;

  // declare global key for tutorial use
  final GlobalKey _keyTutorPrivateData = GlobalKey();
  final GlobalKey _keyTutorQr = GlobalKey();
  final GlobalKey _keyTutorLicense = GlobalKey();
  final GlobalKey _keyTutorAccountMenu = GlobalKey();
  final GlobalKey _keyTutorAgentMenu = GlobalKey();
  final GlobalKey _keyTutorAppMenu = GlobalKey();

  List<TutorModel> tutorProfile = [];

  RxBool isShowAgentMenu = true.obs;

  @override
  void onInit() async {
    super.onInit();
    _getArguments();
    isShowAgentMenu.value =
        (!userLevel.value.inList([
          kUserLevelBdm,
          kUserLevelABDD,
          kUserLevelBDD,
          kUserLevelHOS,
          kUserLevelCAO,
          '-',
        ]));

    _assignTutor();
    prefs = await SharedPreferences.getInstance();
    Future.delayed(Duration(seconds: 1)).then((val) async {
      await api.getProfile(controllers: this);
      getBiometricStatus();
    });
  }

  void _assignTutor() {
    tutorProfile = [
      TutorModel(
        number: 1,
        key: _keyTutorQr,
        title: 'qr_code_str'.tr,
        description: 'tutor_qr_code_str'.tr,
      ),
      TutorModel(
        number: 2,
        key: _keyTutorLicense,
        title: 'label_lisence'.tr,
        description: 'tutor_license_str'.tr,
      ),
      TutorModel(
        number: 3,
        key: _keyTutorAccountMenu,
        title: 'account_setting_str'.tr,
        description: 'tutor_desc_account_setting_str'.tr,
      ),
      if (isShowAgentMenu.value)
        TutorModel(
          number: 4,
          key: _keyTutorAgentMenu,
          title: 'label_download_pdf'.tr,
          description: 'tutor_desc_download_pdf_agent_str'.tr,
        ),
      TutorModel(
        number: isShowAgentMenu.value ? 5 : 4,
        key: _keyTutorAppMenu,
        title: 'label_app_setting'.tr,
        description: 'tutor_desc_app_setting_str'.tr,
      ),
    ];
  }

  getBiometricStatus() async {
    bool isActive = prefs.getBool(kStorageIsBiometricActive) ?? false;
    isBioActive.value = isActive;
  }

  setBiometric() async {
    final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    final bool canAuthenticate =
        canAuthenticateWithBiometrics || await auth.isDeviceSupported();

    List bioList = await auth.getAvailableBiometrics();

    if (canAuthenticate && bioList.isNotEmpty) {
      isBioActive.value = !isBioActive.value;
    } else {
      isBioActive.value = false;
      Get.snackbar(
        'title_bio_failed'.tr,
        'subtitle_bio_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
    }
    Utils.setBiometric();
  }

  @override
  void load() async {
    super.load();
    setLoading(true);
    await api.getProfile(controllers: this, debug: true);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    if (requestCode == kReqPerformLogout) {
      Utils.setLoggedOut();
      return;
    }
    parseData(response);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    setLoading(false);
  }

  String get dataQrCode {
    return 'Kode Agent : ${userData.value.agentCode}\nNama Agent : ${userData.value.agentName}\nKode Cabang : ${userData.value.branchCode}\nChannel : ${userData.value.channel}';
  }

  parseData(response) async {
    try {
      UserModels data = UserModels.fromJson(response);
      userData.value = data;
      // set local storage
      if (prefs.getString(kStorageUserFirestoreId) == '') {
        await prefs.setString(
          kStorageUserFirestoreId,
          'agents-${data.agentCode}-${data.id}',
        );
      } else {
        await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
        await prefs.setString(
          kStorageAgentCode,
          data.agentCode != null ? data.agentCode! : data.username!,
        );
        await prefs.setString(
          kStorageAgentName,
          data.name ?? data.agentName ?? '-',
        );
        await prefs.setString(
          kStorageUserFirestoreId,
          'agents-${data.agentCode}-${data.id}',
        );
      }
      // set currentUserLevel
      await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
      await prefs.setString(
        kStorageAgentCode,
        data.agentCode != null ? data.agentCode! : data.username!,
      );
      await prefs.setString(
        kStorageAgentName,
        data.name ?? data.agentName ?? '-',
      );
      await prefs.setString(kStorageAgentBranch, data.branchName ?? '-');
      await prefs.setString(kStorageUserLevel, data.level ?? '-');
      await prefs.setString(kStorageUserLevelComplete, data.roles?.code ?? '-');
      userLevel.value = data.roles?.code ?? '-';
      FirestoreServices().getInitialValue();
    } catch (e) {
      // print('here err $e');
    }
  }

  performLoggedOut() async {
    setLoading(true);
    await api.performLogout(controllers: this, code: kReqPerformLogout);
  }

  void _getArguments() {
    if (Get.arguments != null) {
      if (Get.arguments['level'] != null) {
        userLevel.value = Get.arguments['level'];
      }
      if (Get.arguments['start_tutorial'] == true) {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          _startTutorial();
        });
      }
    }
  }

  void _startTutorial() {
    if (showCaseContext != null) {
      final keys = tutorProfile.map((e) => e.key).toList();
      ShowCaseWidget.of(showCaseContext!).startShowCase(keys);
    }
  }
}

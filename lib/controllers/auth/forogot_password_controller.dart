import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:pdl_superapp/utils/env_loader.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';

class ForogotPasswordController extends BaseControllers {
  TextEditingController agentCodeTextController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  RxBool isBtnAvailabel = false.obs;
  RxBool isInvalid = false.obs;
  RxBool isEmailInvalid = false.obs;
  RxString errorText = ''.obs;
  RxString emailErrorText = ''.obs;

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    Get.offNamed(
      Routes.FORGOT_PASSWORD_SENT,
      arguments: {
        'email': emailTextController.text,
        'email_encrypted': response['email'],
        'agentCode': agentCodeTextController.text,
      },
    );
  }
  // http://superapp-nlb-73057e211678c433.elb.ap-southeast-3.amazonaws.com/
  // forget-password/
  // reset?
  // token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwMDAwODAyMSIsInJvbGVzIjpbIlJPTEVfQUdFX0JEIl0sImV4cCI6MTc0Mjg1ODA5MiwiaWF0IjoxNzQyODU3MTkyfQ.mV96m9Jggl2f-_A3Iikp5mkM51s0Qbt5T6gyCw5UyM779EunbXIl6zXYr9kjajKGSQUJkHiV22LyARJtXFHVmA

  // ? and = should be parsed
  // xcrun simctl openurl booted https://yourDomain.com/path

  // adb shell am start -a android.intent.action.VIEW \
  //   -c android.intent.category.BROWSABLE \
  //   -d https://nrwvafwa4f.execute-api.ap-southeast-3.amazonaws.com/dev/agent/forget-password/reset%3Ftoken%3DeyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwMDAwODAyMSIsInJvbGVzIjpbIlJPTEVfQUdFX0JEIl0sImV4cCI6MTc0MzA0NDc5MiwiaWF0IjoxNzQzMDQzODkyfQ.o5Xw4S8V7E5OkWwcSpfEK2WB8jo-RpUozjYoMuBFlQRDvnqOCMSwaqZSiuBvUBW2b9ywoYlfdr6P4PsCREU6ew \
  //   id.co.panindaiichilife.quantumx360

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    isInvalid.value = true;
    // Clear email validation error when showing API error
    isEmailInvalid.value = false;
    emailErrorText.value = '';
    errorText.value =
        response.body['error_description'] ??
        'Kode agen atau alamat email tidak sesuai';
  }

  /// Validates email format using regex
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// Validates email and updates error state
  void _validateEmail() {
    final email = emailTextController.text.trim();

    if (email.isEmpty) {
      isEmailInvalid.value = false;
      emailErrorText.value = '';
    } else if (!_isValidEmail(email)) {
      isEmailInvalid.value = true;
      emailErrorText.value = 'Format email tidak valid';
    } else {
      isEmailInvalid.value = false;
      emailErrorText.value = '';
    }
  }

  onChangeText() {
    // Clear general error when user starts typing
    if (isInvalid.value) {
      isInvalid.value = false;
      errorText.value = '';
    }

    // Validate email format
    _validateEmail();

    // Check if button should be enabled
    final isAgentCodeValid = agentCodeTextController.text.isNotEmpty;
    final isEmailValid =
        emailTextController.text.isNotEmpty && !isEmailInvalid.value;

    isBtnAvailabel.value = isAgentCodeValid && isEmailValid;
  }

  onTapNext() {
    performNext();
  }

  performNext() async {
    // Reset all error states
    isInvalid.value = false;
    errorText.value = '';

    // Validate email format before proceeding
    _validateEmail();
    if (isEmailInvalid.value) {
      return; // Don't proceed if email is invalid
    }

    String secretKey = EnvLoader.get("PRIVATE_KEY");

    List<int> bytes = base64.decode(secretKey);
    String decodedStr = utf8.decode(bytes);

    secretKey = decodedStr;

    final username = Encryption(
      secretKey,
    ).doencrypt(agentCodeTextController.text, agentCodeTextController.text);

    final email = Encryption(
      secretKey,
    ).doencrypt(emailTextController.text, emailTextController.text);

    var data = {"username": username, "email": email};
    await api.performForgotPassword(controllers: this, data: data);
  }
}

import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/validasi_hirarki_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ValidasiBDController extends BaseControllers {
  Rx<ValidasiHirarkiModel?> validasiData = Rx<ValidasiHirarkiModel?>(null);
  late SharedPreferences prefs;

  // Dropdown options for BD validasi promosi
  final Map<String, String> dropdownOptions = {
    'pribadi-1': 'Group Pribadi 1',
    'pribadi-2': 'Group Pribadi 2',
    'G1': 'Secara G1',
    'GAll': 'Semua Generasi',
  };

  // Selected dropdown option
  RxString selectedOption = 'pribadi-1'.obs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchValidasiData();
  }

  // Fetch data based on selected option
  fetchValidasiData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';
    
    prefs = await SharedPreferences.getInstance();
    final agentCode = prefs.getString(kStorageAgentCode);
    
    if (agentCode == null) {
      hasError.value = true;
      errorMessage.value = 'Agent code not found';
      setLoading(false);
      return;
    }

    try {
      switch (selectedOption.value) {
        case 'pribadi-1':
          await api.getValidasiHirarki(
            controllers: this, 
            code: kReqGetValidasiHirarki, 
            params: "agentCode=$agentCode&type=group"
          );
          break;
        case 'pribadi-2':
          await api.getValidasiHirarki(
            controllers: this, 
            code: kReqGetValidasiHirarki, 
            params: "agentCode=$agentCode&type=leader"
          );
          break;
        case 'G1':
        case 'GAll':
          // For both G1 and GAll, we use the same endpoint but will display different data
          await api.getValidasiG1(
            controllers: this, 
            code: kReqGetValidasiHirarki, 
            params: "agentCode=$agentCode"
          );
          break;
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error fetching data: $e';
      setLoading(false);
    }
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);
    parseData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = response.statusText ?? 'Error loading data';
    setLoading(false);
  }

  void parseData(response) {
    if (response == null || (response is List && response.isEmpty)) {
      hasError.value = true;
      errorMessage.value = 'No data available';
      return;
    }

    try {
      // If response is a list, take the first item
      final data = response is List ? response[0] : response;
      ValidasiHirarkiModel parsedData = ValidasiHirarkiModel.fromJson(data);
      validasiData.value = parsedData;
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error parsing data: $e';
    }
  }

  // Change the selected option and fetch new data
  void changeOption(String option) {
    if (selectedOption.value != option) {
      selectedOption.value = option;
      fetchValidasiData();
    }
  }

  // Helper method to format percentage values
  String formatPercentage(double value) {
    return '${value.toStringAsFixed(0)}%';
  }

  // Helper method to format currency values
  String formatCurrency(double value) {
    if (value >= 1000000000) {
      return 'Rp${(value / 1000000000).toStringAsFixed(1)} M';
    } else if (value >= 1000000) {
      return 'Rp${(value / 1000000).toStringAsFixed(1)} Jt';
    } else {
      return 'Rp${value.toStringAsFixed(0)}';
    }
  }
}

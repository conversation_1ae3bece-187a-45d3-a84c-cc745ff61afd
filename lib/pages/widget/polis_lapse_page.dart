import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/filter_month_year.dart';
import 'package:pdl_superapp/controllers/widget/polis_lapse_page_controller.dart';
import 'package:pdl_superapp/models/policy_lapsed_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/num_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';

class PolisLapsePage extends StatelessWidget {
  PolisLapsePage({super.key}) {
    // Initialize date formatting for Indonesian locale
    initializeDateFormatting('id_ID', null);
  }

  final PolisLapsePageController controller = Get.put(
    PolisLapsePageController(
      agentCode: Get.parameters['agentCode'],
      defaultTab: int.tryParse(Get.parameters['withDownline'].toString()) ?? 0,
    ),
    tag: "polis-lapse-page-${DateTime.timestamp()}",
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'lapsed_policy_str'.tr,
      scrollPhysics: NeverScrollableScrollPhysics(),
      child: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height - (Get.statusBarHeight - paddingSmall),
            padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
            child: Obx(() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tab selector for non-BP roles - Fixed at top
                  if (controller.level != kLevelBP) _buildTabSelector(context),

                  // Search and filter - Always visible
                  _buildSearchFilterBar(context),

                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async => controller.refreshData(),
                      child: SingleChildScrollView(
                        controller: controller.scrollController,
                        child: Column(
                          children: [
                            // Loading indicator
                            if (controller.isLoading.value &&
                                controller.policyLapsedList.isEmpty)
                              SizedBox(
                                width: Get.width,
                                height: 200,
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),

                            // Error message
                            if (controller.hasError.value)
                              Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.error_outline,
                                      size: 48,
                                      color: Colors.red,
                                    ),
                                    const SizedBox(height: paddingSmall),
                                    Text(
                                      'Error: ${controller.errorMessage.value}',
                                    ),
                                    const SizedBox(height: paddingMedium),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        ElevatedButton(
                                          onPressed:
                                              () => controller.refreshData(),
                                          child: Text('retry_str'.tr),
                                        ),
                                        const SizedBox(width: paddingMedium),
                                        if (controller
                                                .activeFilters
                                                .value
                                                .isNotEmpty ||
                                            controller.searchQuery.isNotEmpty)
                                          OutlinedButton(
                                            onPressed: () {
                                              controller.resetFilters();
                                              controller.refreshData();
                                              controller.searchTextController
                                                  .clear();
                                            },
                                            child: const Text(
                                              'Reset Filter & Coba Lagi',
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                            // Empty state
                            if (!controller.isLoading.value &&
                                !controller.hasError.value &&
                                controller.policyLapsedList.isEmpty)
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(paddingMedium),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.hourglass_empty,
                                        size: 48,
                                        color: Colors.grey.shade400,
                                      ),
                                      const SizedBox(height: paddingMedium),
                                      const Text(
                                        "Kami tidak menemukan data yang anda cari",
                                      ),
                                      const SizedBox(height: paddingSmall),
                                      if (controller
                                              .activeFilters
                                              .value
                                              .isNotEmpty ||
                                          controller.searchQuery.isNotEmpty)
                                        TextButton(
                                          onPressed: () {
                                            controller.resetFilters();
                                            controller.refreshData();
                                            controller.searchTextController
                                                .clear();
                                          },
                                          child: const Text('Reset Filter'),
                                        ),
                                    ],
                                  ),
                                ),
                              ),

                            // No results message
                            if (!controller.isLoading.value &&
                                !controller.hasError.value &&
                                controller.policyLapsedList.isNotEmpty &&
                                controller.filteredList.isEmpty)
                              Padding(
                                padding: const EdgeInsets.all(paddingLarge),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.search_off,
                                      size: 48,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: paddingMedium),
                                    Text(
                                      'Tidak ada hasil yang sesuai dengan pencarian',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    if (controller
                                            .activeFilters
                                            .value
                                            .isNotEmpty ||
                                        controller.searchQuery.isNotEmpty)
                                      TextButton(
                                        onPressed:
                                            () => controller.resetFilters(),
                                        child: const Text('Reset pencarian'),
                                      ),
                                  ],
                                ),
                              ),
                            // Policy list
                            if (!controller.isLoading.value &&
                                !controller.hasError.value &&
                                controller.policyLapsedList.isNotEmpty &&
                                controller.filteredList.isNotEmpty)
                              Column(
                                children: [
                                  ListView.separated(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    padding: EdgeInsets.zero,
                                    itemCount: controller.filteredList.length,
                                    separatorBuilder:
                                        (context, index) =>
                                            const Divider(thickness: 3),
                                    itemBuilder: (context, index) {
                                      final policy =
                                          controller.filteredList[index];
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 16.0,
                                        ),
                                        child: PolicyHolderItem(
                                          policy: policy,
                                          formatDate: controller.formatDate,
                                          formatCurrency:
                                              controller.formatCurrency,
                                          showAgentInfo:
                                              controller
                                                  .selectedSection
                                                  .value ==
                                              1,
                                        ),
                                      );
                                    },
                                  ),
                                  // Loading indicator for pagination
                                  if (controller.isLoadingMore.value)
                                    SizedBox(
                                      width: Get.width,
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    ),
                                  SizedBox(height: paddingExtraLarge),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
          // Scroll to top button
          Obx(() {
            return AnimatedPositioned(
              duration: const Duration(milliseconds: 300),
              bottom: controller.showScrollToTopButton.value ? 20 : -60,
              right: 20,
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                onPressed: controller.scrollToTop,
                child: const Icon(Icons.keyboard_arrow_up),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSearchFilterBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: paddingMedium, bottom: paddingMedium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        spacing: paddingMedium,
        children: [
          // Search bar
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: paddingSmall,
                vertical: paddingSmall,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(radiusSmall),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                spacing: paddingSmall,
                children: [
                  // Search mode dropdown (replacing search icon)
                  DropdownButton<String>(
                    value: controller.searchMode.value,
                    underline: const SizedBox(),
                    icon: const Icon(Icons.arrow_drop_down, size: 18),
                    iconSize: 18,
                    isDense: true,
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                    items: [
                      DropdownMenuItem(
                        value: 'policyHolderName',
                        child: Text(
                          'Nama',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                      DropdownMenuItem(
                        value: 'spajNumber',
                        child: Text(
                          'SPAJ No.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                      DropdownMenuItem(
                        value: 'policyNumber',
                        child: Text(
                          'Polis No.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        controller.setSearchMode(value);
                      }
                    },
                  ),
                  // Vertical divider
                  Container(height: 24, width: 1, color: Colors.grey.shade300),
                  // Search field
                  Expanded(
                    child: TextField(
                      controller: controller.searchTextController,
                      decoration: InputDecoration(
                        hintText: _getSearchHint(controller.searchMode.value),
                        border: InputBorder.none,
                        isDense: true,
                        isCollapsed: true,
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: paddingExtraSmall,
                        ),
                        fillColor: Colors.transparent,
                      ),
                      onChanged: (value) {
                        // Just update the value but don't trigger search
                        controller.searchQuery.value = value;
                        if (value.length > 2) {
                          controller.applyFilters();
                        } else if (value.isEmpty) {
                          controller.applyFilters();
                        }
                      },
                      onSubmitted: (value) {
                        // Apply search when Enter is pressed
                        controller.searchQuery.value = value;
                        controller.applyFilters();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder:
                    (context) => FilterMonthYear(
                      initialFilters: controller.activeFilters.value,
                    ),
              ).then((result) {
                if (result != null) {
                  controller.activeFilters.value = result;
                  // Apply filters after setting the new values
                  controller.applyFilters();
                }
              });
            },
            borderRadius: BorderRadius.circular(radiusSmall),
            child: Container(
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: paddingSmall),
              decoration: BoxDecoration(
                color:
                    controller.activeFilters.value.isNotEmpty
                        ? const Color(0xFFE3F2FD)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(radiusSmall),
                border: Border.all(
                  color:
                      controller.activeFilters.value.isNotEmpty
                          ? const Color(0xFF1976D2)
                          : Colors.grey.shade300,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 20,
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Filter',
                    style: TextStyle(
                      color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                      fontWeight:
                          controller.activeFilters.value.isNotEmpty
                              ? FontWeight.w500
                              : FontWeight.normal,
                    ),
                  ),
                  if (controller.activeFilters.value.containsKey('startDate') &&
                      controller.activeFilters.value.containsKey('endDate'))
                    Container(
                      margin: const EdgeInsets.only(left: 6),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1976D2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _formatDateRange(controller.activeFilters.value),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Format date range for display in filter badge
  String _formatDateRange(Map<String, dynamic> filters) {
    try {
      DateTime startDate;
      DateTime endDate;

      // Use the DateTime objects if available, otherwise parse from string
      if (filters.containsKey('startDateTime') &&
          filters.containsKey('endDateTime')) {
        startDate = filters['startDateTime'];
        endDate = filters['endDateTime'];
      } else {
        startDate = DateTime.parse(filters['startDate']);
        endDate = DateTime.parse(filters['endDate']);
      }

      // Use a simpler format that doesn't require locale initialization
      final dateFormat = DateFormat('MM/yy');
      return '${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}';
    } catch (e) {
      return 'Tanggal';
    }
  }

  // Get search hint based on search mode
  String _getSearchHint(String searchMode) {
    switch (searchMode) {
      case 'policyHolderName':
        return 'Cari nama pemegang polis...';
      case 'spajNumber':
        return 'Cari nomor SPAJ...';
      case 'policyNumber':
        return 'Cari nomor polis...';
      default:
        return 'Cari...';
    }
  }

  // Tab selector for non-BP roles
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 0
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 0
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 1
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 1
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    controller.level == kLevelBD ? 'Group' : 'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}

class PolicyHolderItem extends StatelessWidget {
  final PolicyLapsedModel policy;
  final Function(String) formatDate;
  final Function(double, String) formatCurrency;
  final bool showAgentInfo;

  const PolicyHolderItem({
    super.key,
    required this.policy,
    required this.formatDate,
    required this.formatCurrency,
    this.showAgentInfo = false,
  });

  @override
  Widget build(BuildContext context) {
    // Define the fields to display
    List<Map<String, dynamic>> fields = [];

    // Add standard fields
    fields.addAll([
      {
        'label': 'policy_holder_name_str'.tr,
        'value': policy.policyHolderName,
        'isStatus': false,
      },
      {
        'label': 'policy_no_str',
        'value': policy.policyNumber,
        'isStatus': false,
      },
      {
        'label': 'basic_premium_str'.tr,
        'value':
            (double.tryParse(policy.basicPremium.toString()) ?? 0)
                .formatCurrencyId(),
        'isStatus': false,
      },
      {
        'label': 'payment_period_str'.tr,
        'value': "${policy.paymentFrequency.toString().toLowerCase()}_str".tr,
        'isStatus': false,
      },
      {
        'label': 'lapse_date_str'.tr,
        'value': formatDate(policy.lapseDate),
        'isStatus': false,
      },
      // {
      //   'label': 'status_str'.tr,
      //   'value': "${policy.policyStatus.toString().toLowerCase()}_status_tr".tr,
      //   'isStatus': true,
      // },
    ]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: paddingSmall,
      children: [
        if (showAgentInfo)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: paddingSmall,
            children: [
              Text(
                policy.agentName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(policy.agentCode, style: const TextStyle(fontSize: 14)),
              Divider(),
            ],
          ),

        // Build all field rows
        ...fields.asMap().entries.map((entry) {
          final field = entry.value;
          final bool alignLeft = field['alignLeft'] == true;

          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                field['label'] as String,
                style: const TextStyle(color: Colors.grey, fontSize: 16),
              ),
              const Spacer(),
              Text(
                field['value'] as String,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: alignLeft ? TextAlign.start : TextAlign.end,
              ),
            ],
          );
        }),
      ],
    );
  }
}

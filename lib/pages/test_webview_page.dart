import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/webview.dart';

class TestWebViewPage extends StatelessWidget {
  const TestWebViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test WebView'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Test WebView URLs',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            
            // Test dengan Google
            ElevatedButton(
              onPressed: () {
                Get.to(() => WebviewPage(fullUrl: 'https://www.google.com'));
              },
              child: Text('Test Google'),
            ),
            SizedBox(height: 8),
            
            // Test dengan Flutter.dev
            ElevatedButton(
              onPressed: () {
                Get.to(() => WebviewPage(fullUrl: 'https://flutter.dev'));
              },
              child: Text('Test Flutter.dev'),
            ),
            SizedBox(height: 8),
            
            // Test dengan GitHub
            ElevatedButton(
              onPressed: () {
                Get.to(() => WebviewPage(fullUrl: 'https://github.com'));
              },
              child: Text('Test GitHub'),
            ),
            SizedBox(height: 8),
            
            // Test dengan URL yang mungkin bermasalah
            ElevatedButton(
              onPressed: () {
                Get.to(() => WebviewPage(fullUrl: 'https://httpbin.org/html'));
              },
              child: Text('Test HTTPBin HTML'),
            ),
            SizedBox(height: 8),
            
            // Test dengan URL yang tidak valid
            ElevatedButton(
              onPressed: () {
                Get.to(() => WebviewPage(fullUrl: 'invalid-url'));
              },
              child: Text('Test Invalid URL'),
            ),
            
            SizedBox(height: 24),
            Text(
              'Instructions:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              '1. Test each URL to see if WebView loads properly\n'
              '2. Check if loading indicator appears\n'
              '3. Verify error handling for invalid URLs\n'
              '4. Test navigation and scrolling\n'
              '5. Check if close button works',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/inbox/inbox_list_controller.dart';
import 'package:pdl_superapp/models/menu_inbox_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class DrawerInbox extends StatelessWidget {
  const DrawerInbox({super.key, required this.onChoose});
  final Function(MenuInboxModel data) onChoose;

  InboxListController get controller => Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Get.isDarkMode ? kColorBgDark : kColorBgLight,
      padding: EdgeInsets.only(top: 40, left: 10, right: 10, bottom: 10),
      child: Material(
        color: Colors.transparent,
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            SizedBox(
              height: 60,
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Text(
                      "Inbox",
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Icon(Icons.close),
                    ),
                  ),
                ],
              ),
            ),

            ...controller.menuInbox.map(
              (e) => Padding(
                padding: const EdgeInsets.only(bottom: 2.0),
                child: ListTile(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  selected: e.inboxSelected,
                  selectedTileColor: kColorGlobalBlue,
                  selectedColor: Colors.white,
                  leading: Utils.cachedSvgWrapper(
                    width: 25,
                    'icon/${e.icon}.svg',
                    color: e.inboxSelected ? Colors.white : Colors.blue,
                  ),
                  titleTextStyle: TextStyle(
                    fontSize: 12,
                    color: kColorTextLight,
                  ),
                  title: Text(
                    e.title,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: e.inboxSelected ? Colors.white : Colors.black,
                      fontWeight:
                          e.inboxSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  onTap: () {
                    onChoose(e);
                  },
                  trailing:
                      e.inboxCount > 0
                          ? Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.red[100],
                              shape: BoxShape.circle,
                            ),
                            child: Text(
                              e.inboxCount >= 100
                                  ? "99+"
                                  : e.inboxCount.toString(),
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          )
                          : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

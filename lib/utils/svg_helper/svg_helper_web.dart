import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Web-specific SVG helper that uses SvgPicture.network
/// since cached_network_svg_image is not supported on web
Widget buildCachedNetworkSvg(
  String url, {
  double? width,
  double? height,
  Color? color,
  BoxFit? fit,
  Widget? errorWidget,
}) {
  return SvgPicture.network(
    url,
    width: width,
    height: height,
    colorFilter: color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
    fit: fit ?? BoxFit.contain,
    placeholderBuilder: (context) => Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const CircularProgressIndicator(),
    ),
    // ignore: deprecated_member_use
    errorBuilder: (context, error, stackTrace) => errorWidget ?? Container(),
  );
}

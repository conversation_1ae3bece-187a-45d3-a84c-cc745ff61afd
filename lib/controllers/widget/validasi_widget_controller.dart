import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/promosi_bm_tab_controller.dart';
import 'package:pdl_superapp/controllers/widget/validasi_bd_controller.dart';
import 'package:pdl_superapp/controllers/widget/validasi_tab_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ValidasiWidgetController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Validasi, 1 for Promosi BM/BD
  String level = '';

  // Controllers
  late ValidasiTabController validasiController;
  late PromosiBMTabController promosiBMController;
  late ValidasiBDController validasiBDController;
  late SharedPreferences prefs;

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    setLoading(true);
    validasiController = Get.put(
      ValidasiTabController(),
      tag: Utils.getRandomString(),
    );
    promosiBMController = Get.put(
      PromosiBMTabController(),
      tag: Utils.getRandomString(),
    );
    validasiBDController = Get.put(
      ValidasiBDController(),
      tag: Utils.getRandomString(),
    );

    refreshData();
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections
  void switchToValidasi() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToPromosiBM() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (level == kLevelBD) {
      validasiBDController.fetchValidasiData();
    } else if (selectedSection.value == 0) {
      validasiController.fetchValidasiData();
    } else {
      promosiBMController.fetchPromosiAgentData();
    }
    setLoading(false);
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/languages/languages.dart';

// Controller for handling language toggle state
class LanguagToggleController extends BaseControllers {
  static LanguagToggleController get to => Get.find<LanguagToggleController>();

  // Observable boolean for language state (true = Indonesia, false = English)
  final RxBool isIndo = false.obs;

  @override
  void onInit() {
    super.onInit();
    // print(Get.isDarkMode); for get current active themeMode
    if (Get.locale == Locale('en', 'US')) {
      isIndo.value = false;
    } else {
      isIndo.value = true;
    }
  }

  @override
  void dispose() {
    super.dispose();
    dispose();
  }

  void toggleLanguage() {
    isIndo.value = !isIndo.value;

    if (isIndo.isFalse) {
      Languages().changeLocale('English');
    } else {
      Languages().changeLocale('Indonesia');
    }
  }
}

class LanguageToggle extends StatefulWidget {
  const LanguageToggle({super.key});

  @override
  State<LanguageToggle> createState() => _LanguageToggleState();
}

class _LanguageToggleState extends State<LanguageToggle>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _positionAnimation;
  late Animation<Color?> _enColorAnimation;
  late Animation<Color?> _idColorAnimation;

  // Initialize the controller if it doesn't exist yet
  final LanguagToggleController _languagToggleController = Get.put(
    LanguagToggleController(),
  );

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );

    _positionAnimation = Tween<double>(begin: 4, end: 28).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Define color animations for EN text
    _enColorAnimation = ColorTween(
      begin: Colors.transparent,
      end: Get.isDarkMode ? kColorTextDark : kColorTextLight,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Define color animations for ID text
    _idColorAnimation = ColorTween(
      begin: Get.isDarkMode ? kColorTextDark : kColorTextLight,
      end: Colors.transparent,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Set initial position based on current language state
    if (!_languagToggleController.isIndo.value) {
      _animationController.value = 1.0;
    }

    // Listen for changes to the language and update animation accordingly
    _languagToggleController.isIndo.listen((isIndo) {
      if (isIndo) {
        _animationController.reverse();
      } else {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _languagToggleController.toggleLanguage,
      child: Container(
        width: 57,
        height: 32,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.secondary, //Background color
          borderRadius: BorderRadius.circular(57),
        ),
        child: Stack(
          children: [
            AnimatedBuilder(
              animation: _positionAnimation,
              builder: (context, child) {
                return Positioned(
                  left: _positionAnimation.value,
                  child: child!,
                );
              },
              child: Container(
                width: 25,
                height: 25,
                margin: EdgeInsets.only(top: 3.5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),

                  border: Border.all(color: Color(0xFFD9D9D9), width: 1.5),
                ),
                child: SvgPicture.asset(
                  _languagToggleController.isIndo.isTrue ? kFlagId : kFlagUs,
                ),
              ),
            ),
            SizedBox(
              width: 60,
              height: 32,
              child: Row(
                children: [
                  Expanded(
                    child: AnimatedBuilder(
                      animation: _enColorAnimation,
                      builder: (context, _) {
                        return Text(
                          'EN',
                          style: Theme.of(
                            context,
                          ).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _enColorAnimation.value,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: AnimatedBuilder(
                      animation: _idColorAnimation,
                      builder: (context, _) {
                        return Text(
                          'ID',
                          style: Theme.of(
                            context,
                          ).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _idColorAnimation.value,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/polis_lapse_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/polis_lapse_team_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PolisLapseController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team/Group
  bool isShowOtherAgent = false;
  final String? agentCode;

  // Controllers for different tabs
  late PolisLapseIndividuController individuController = Get.put(
    PolisLapseIndividuController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  late PolisLapseTeamController teamController = Get.put(
    PolisLapseTeamController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  PolisLapseController({this.isShowOtherAgent = false, this.agentCode}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    setLoading(true);
    refreshData();
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (level == kLevelBP || selectedSection.value == 0) {
      // Individual view
      individuController.fetchPolicyLapsedData();
    } else {
      // Team/Group view
      teamController.fetchPolicyLapsedData();
    }
    setLoading(false);
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Format currency
  String formatCurrency(double amount, String currency) {
    final formatter = NumberFormat.currency(symbol: currency, decimalDigits: 2);
    return formatter.format(amount);
  }
}

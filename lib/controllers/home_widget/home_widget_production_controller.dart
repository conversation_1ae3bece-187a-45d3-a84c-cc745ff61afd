import 'package:get/get_connect/http/src/response/response.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/widget/widget_production_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeWidgetProductionController extends BaseControllers {
  late SharedPreferences prefs;
  RxString userLevel = ''.obs;
  // core data
  Rx<WidgetProductionSumModels> widgetDataMonth = Rx(
    WidgetProductionSumModels(),
  );
  Rx<WidgetProductionSumModels> widgetDataYear = Rx(
    WidgetProductionSumModels(),
  );

  RxString selectedType = kSwitchMonthly.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
    load();
  }

  @override
  void load() async {
    final year = DateTime.now().year.toString();
    final month = DateTime.now().month.toString();
    final paramsMonthly = 'year=$year&month=$month';
    final paramsYearly = 'year=$year';
    super.load();
    await api.getWidgetProductionSum(
      controllers: this,
      params: paramsMonthly,
      code: kReqGetWgtSumMonth,
    );
    await api.getWidgetProductionSum(
      controllers: this,
      params: paramsYearly,
      code: kReqGetWgtSumYear,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    parseData(response, requestCode);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  parseData(response, int reqCode) {
    WidgetProductionSumModels data = WidgetProductionSumModels.fromJson(
      response,
    );
    switch (reqCode) {
      case kReqGetWgtSumMonth:
        widgetDataMonth.value = data;
        break;
      case kReqGetWgtSumYear:
        widgetDataYear.value = data;
        break;
      default:
    }
  }
}

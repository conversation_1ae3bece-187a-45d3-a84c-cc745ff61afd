import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/utils.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(54),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFF00B4DB), Color(0xFF0083B0)],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0xFF8FD8FF).withValues(alpha: 0.4),
                      blurRadius: 10, // Softness of the shadow
                      spreadRadius: 0, // How much the shadow spreads
                      offset: Offset(0, 0), // Shadow position
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(10),
                  child: Utils.cachedImageWrapper('connected.png'),
                ),
              ),
              SizedBox(height: 20),
              CircularProgressIndicator(),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/controllers/widget/favorite_widget_controller.dart';

import 'package:pdl_superapp/models/draggable_widget_model.dart';
import 'package:pdl_superapp/models/widget_item_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

// The favorite widget page
// ignore: must_be_immutable
class FavoriteWidgetPage extends StatefulWidget {
  const FavoriteWidgetPage({super.key});

  @override
  State<FavoriteWidgetPage> createState() => _FavoriteWidgetPageState();
}

class _FavoriteWidgetPageState extends State<FavoriteWidgetPage> {
  late FavoriteWidgetController controller;

  @override
  void initState() {
    super.initState();

    // Always create a new controller instance for this page
    // This ensures clean state and prevents conflicts
    controller = Get.put(
      FavoriteWidgetController(),
      permanent: false, // Allow disposal when page is closed
    );
  }

  @override
  void dispose() {
    // Clean up controller when page is disposed
    try {
      Get.delete<FavoriteWidgetController>();
    } catch (e) {
      // Controller might already be disposed
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BaseDetailPage(
        title: 'widget_favorite_title_str'.tr,
        controller: controller,
        backEnabled: true,
        onRefresh: () async => controller.refreshData(),
        bottomAction:
            controller.hasChanges.value
                ? () => controller.saveFavorites()
                : null,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: paddingMedium),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: paddingLarge),
                _buildFavoritesSection(context),
                SizedBox(height: paddingLarge),
                _buildAvailableWidgetsSection(context),
                SizedBox(height: paddingLarge * 2),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Favorites section
  Widget _buildFavoritesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'your_fave_widget_str'.tr,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: paddingMedium),
        _buildDragTarget(
          context,
          isFavoriteTarget: true,
          itemList: controller.favoriteWidgets,
          onDrop: controller.onFavoriteDrop,
          emptyMessage:
              'Drag widget dari bawah ke sini untuk menambahkan ke favorit',
          actionIcon: Icons.remove_circle,
          onActionPressed: controller.removeFromFavorites,
          actionColor: Colors.red,
        ),
      ],
    );
  }

  // Available widgets section
  Widget _buildAvailableWidgetsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'choose_fave_widget_desc_str'.tr,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w400),
        ),
        SizedBox(height: paddingMedium),
        _buildDragTarget(
          context,
          isFavoriteTarget: false,
          itemList: controller.availableWidgets,
          onDrop: controller.onAvailableDrop,
          emptyMessage: 'Semua widget sudah ditambahkan ke favorit',
          actionIcon: Icons.add_circle,
          onActionPressed: controller.addToFavorites,
          actionColor: kColorGlobalGreen,
        ),
      ],
    );
  }

  // Drag target widget for both sections
  Widget _buildDragTarget(
    BuildContext context, {
    required bool isFavoriteTarget,
    required RxList<WidgetItemModel> itemList,
    required Function(DraggableWidget) onDrop,
    required String emptyMessage,
    required IconData actionIcon,
    required Function(WidgetItemModel) onActionPressed,
    required Color actionColor,
  }) {
    return DragTarget<DraggableWidget>(
      builder: (context, candidateData, rejectedData) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color:
                candidateData.isNotEmpty
                    ? Theme.of(
                      context,
                    ).colorScheme.primaryContainer.withValues(alpha: 0.3)
                    : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Obx(() {
            if (itemList.isEmpty) {
              return Center(
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: paddingMedium),
                  child: Text(
                    emptyMessage,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                ),
              );
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                childAspectRatio: 0.75,
                crossAxisSpacing: paddingSmall,
                mainAxisSpacing: paddingSmall,
              ),
              itemCount: itemList.length,
              itemBuilder: (context, index) {
                final widgetItem = itemList[index];
                return DragTarget<DraggableWidget>(
                  builder: (context, candidateData, rejectedData) {
                    return Obx(
                      () => _buildDraggableWidget(
                        context,
                        widgetItem,
                        actionIcon,
                        onActionPressed,
                        actionColor,
                        isFavoriteTarget
                            ? SourceType.favorite
                            : SourceType.available,
                        index,
                      ),
                    );
                  },
                  onAcceptWithDetails: (details) {
                    // Handle dropping at specific position in favorites
                    if (isFavoriteTarget) {
                      controller.onFavoriteDropAtPosition(details.data, index);
                    } else {
                      // For available section, just use the regular onDrop
                      onDrop(details.data);
                    }
                  },
                  onWillAcceptWithDetails: (details) {
                    // Only accept if it's a different position or from available to favorite
                    if (isFavoriteTarget) {
                      // If it's the same widget being dropped on itself, reject
                      if (details.data.sourceType == SourceType.favorite &&
                          details.data.sourceIndex == index) {
                        return false;
                      }

                      // Check max limit for favorites
                      return controller.favoriteWidgets.length <
                              FavoriteWidgetController.maxFavoriteWidgets ||
                          controller.favoriteWidgets.any(
                            (w) => w.id == details.data.widget.id,
                          );
                    }

                    // For available section, only accept from favorites
                    return details.data.sourceType == SourceType.favorite;
                  },
                );
              },
            );
          }),
        );
      },
      onAcceptWithDetails: (details) {
        // This is for the entire section
        onDrop(details.data);
      },
      onWillAcceptWithDetails: (details) {
        if (isFavoriteTarget) {
          return controller.favoriteWidgets.length <
                  FavoriteWidgetController.maxFavoriteWidgets ||
              controller.favoriteWidgets.any(
                (w) => w.id == details.data.widget.id,
              );
        }

        // For available section, only accept from favorites
        return details.data.sourceType == SourceType.favorite;
      },
    );
  }

  // Draggable widget item
  Widget _buildDraggableWidget(
    BuildContext context,
    WidgetItemModel widget,
    IconData actionIcon,
    Function(WidgetItemModel) onActionPressed,
    Color actionColor,
    SourceType sourceType,
    int index,
  ) {
    // Check if widget is in favorites to determine if it should be draggable
    bool isInFavorites = controller.favoriteWidgets.any(
      (item) => item.id == widget.id,
    );
    bool isInAvailableSection = sourceType == SourceType.available;
    bool isDraggable = isInAvailableSection ? !isInFavorites : true;

    // If not draggable, just return the widget card with reduced opacity
    if (!isDraggable) {
      return _buildWidgetCard(
        context,
        widget,
        actionIcon,
        onActionPressed,
        actionColor,
      );
    }

    // Create a draggable widget wrapper
    DraggableWidget draggableWidget = DraggableWidget(
      widget: widget,
      sourceType: sourceType,
      sourceIndex: index,
    );

    return Draggable<DraggableWidget>(
      data: draggableWidget,
      feedback: Material(
        elevation: 0,
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 80,
          padding: EdgeInsets.all(paddingSmall),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.all(paddingExtraSmall),
                child: Utils.cachedSvgWrapper(
                  widget.iconUrl,
                  width: 52,
                  height: 52,
                ),
              ),
              SizedBox(height: paddingSmall),
              Text(
                widget.label,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: _buildWidgetCard(
          context,
          widget,
          actionIcon,
          onActionPressed,
          actionColor,
        ),
      ),
      child: _buildWidgetCard(
        context,
        widget,
        actionIcon,
        onActionPressed,
        actionColor,
      ),
    );
  }

  // Widget card
  Widget _buildWidgetCard(
    BuildContext context,
    WidgetItemModel widget,
    IconData actionIcon,
    Function(WidgetItemModel) onActionPressed,
    Color actionColor,
  ) {
    // Check if widget is in favorites to determine if it should be draggable
    bool isInFavorites = controller.favoriteWidgets.any(
      (item) => item.id == widget.id,
    );
    bool isInAvailableSection = actionIcon == Icons.add_circle;
    bool isDraggable = isInAvailableSection ? !isInFavorites : true;

    return Container(
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Stack(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: paddingExtraSmall,
                  horizontal: 10,
                ),
                child: Utils.cachedSvgWrapper(
                  widget.iconUrl,
                  width: 52,
                  height: 52,
                ),
              ),
              // Only show action button if the widget is draggable
              if (isDraggable)
                Positioned(
                  top: 3,
                  right: 0,
                  child: GestureDetector(
                    onTap: () => onActionPressed(widget),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Small white circle background
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            shape: BoxShape.circle,
                          ),
                        ),
                        // Icon on top
                        Icon(actionIcon, color: actionColor, size: 25),
                      ],
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: paddingSmall),
          Text(
            widget.label,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

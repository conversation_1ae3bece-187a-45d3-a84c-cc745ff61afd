import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_upcoming_controller.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_past_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PolisJatuhTempoController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Akan datang, 1 for Sudah Lewat
  bool isShowOtherAgent = false;
  final String? agentCode;

  // Controllers for different tabs
  late PolisJatuhTempoUpcomingController upcomingController = Get.put(
    PolisJatuhTempoUpcomingController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  late PolisJatuhTempoPastController pastController = Get.put(
    PolisJatuhTempoPastController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  PolisJatuhTempoController({this.isShowOtherAgent = false, this.agentCode}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    setLoading(true);
    refreshData();
  }

  // Switch between sections
  void switchToUpcoming() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToPast() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on selected tab
  void refreshData() {
    // Always fetch data for both tabs
    upcomingController.fetchPolicyOverdueData();
    pastController.fetchPolicyOverdueData();
    setLoading(false);
  }
}

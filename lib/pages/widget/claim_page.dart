import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/claim_item.dart';
import 'package:pdl_superapp/components/filter_bottom_sheet.dart';
import 'package:pdl_superapp/controllers/widget/claim_page_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/num_ext.dart';

import '../../utils/utils.dart';

class ClaimPage extends StatelessWidget {
  ClaimPage({super.key});

  final ClaimPageController controller = Get.put(
    ClaimPageController(
      agentCode: Get.parameters['agentCode'],
      withDownline:
          int.tryParse(Get.parameters['withDownline'].toString()) ?? 0,
    ),
    tag: "claim-page-${DateTime.timestamp()}",
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'claim_status_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Loading indicator
              if (controller.isLoading.value)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(paddingLarge),
                    child: CircularProgressIndicator(),
                  ),
                ),

              // Error message
              if (controller.hasError.value)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red,
                      ),
                      const SizedBox(height: paddingSmall),
                      Text('Error: ${controller.errorMessage.value}'),
                      const SizedBox(height: paddingMedium),
                      ElevatedButton(
                        onPressed: () => controller.refreshData(),
                        child: Text('retry_str'.tr),
                      ),
                    ],
                  ),
                ),

              // // Empty state
              // if (!controller.isLoading.value &&
              //     !controller.hasError.value &&
              //     controller.claimTrackingList.isEmpty)
              //   Center(
              //     child: Padding(
              //       padding: EdgeInsets.all(paddingMedium),
              //       child: Text("no_data_tracking_claim_str".tr),
              //     ),
              //   ),

              // Content when we have data
              if (!controller.isLoading.value && !controller.hasError.value)
                Column(
                  children: [
                    const SizedBox(height: 16),

                    // Filter button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      spacing: paddingMedium,
                      children: [
                        // Search bar
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: paddingSmall,
                              vertical: paddingSmall,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(radiusSmall),
                              border: Border.all(color: Colors.grey.shade200),
                            ),
                            child: Row(
                              spacing: paddingSmall,
                              children: [
                                Icon(Icons.search, color: Colors.grey.shade600),
                                Expanded(
                                  child: TextField(
                                    decoration: InputDecoration(
                                      hintText: 'find_name_number_str'.tr,
                                      border: InputBorder.none,
                                      isDense: true,
                                      isCollapsed: true,
                                      contentPadding: EdgeInsets.symmetric(
                                        vertical: paddingExtraSmall,
                                      ),
                                      fillColor: Colors.transparent,
                                    ),
                                    onChanged: (value) {
                                      controller.searchQuery.value = value;
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              builder:
                                  (context) => FilterBottomSheet(
                                    initialFilters: controller.activeFilters,
                                    userLevel: controller.userLevel,
                                  ),
                            ).then((result) {
                              if (result != null) {
                                controller.activeFilters.value = result;
                              }
                            });
                          },
                          borderRadius: BorderRadius.circular(radiusSmall),
                          child: Container(
                            height: 48,
                            padding: const EdgeInsets.symmetric(
                              horizontal: paddingSmall,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  controller.activeFilters.isNotEmpty
                                      ? const Color(0xFFE3F2FD)
                                      : Colors.transparent,
                              borderRadius: BorderRadius.circular(radiusSmall),
                              border: Border.all(
                                color:
                                    controller.activeFilters.isNotEmpty
                                        ? const Color(0xFF1976D2)
                                        : Colors.grey.shade300,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.filter_list,
                                  size: 20,
                                  color:
                                      controller.activeFilters.isNotEmpty
                                          ? const Color(0xFF1976D2)
                                          : Colors.grey.shade700,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'filter_str'.tr,
                                  style: TextStyle(
                                    color:
                                        controller.activeFilters.isNotEmpty
                                            ? const Color(0xFF1976D2)
                                            : Colors.grey.shade700,
                                    fontWeight:
                                        controller.activeFilters.isNotEmpty
                                            ? FontWeight.w500
                                            : FontWeight.normal,
                                  ),
                                ),
                                if (controller.activeFilters.isNotEmpty &&
                                    controller.activeFilters['status'] !=
                                        null &&
                                    (controller.activeFilters['status'] as List)
                                        .isNotEmpty)
                                  Container(
                                    margin: const EdgeInsets.only(left: 6),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF1976D2),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '${(controller.activeFilters['status'] as List).length}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // No results message
                    if (controller.filteredList.isEmpty &&
                        !controller.isLoading.value)
                      Padding(
                        padding: const EdgeInsets.all(paddingLarge),
                        child: Column(
                          children: [
                            Utils.cachedSvgWrapper(
                              'icon/ic-no-results.svg',
                              width: Get.width / 2,
                            ),
                            const SizedBox(height: paddingMedium),
                            Text(
                              'no_data_claim_str'.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.grey.shade600),
                            ),
                            // if (controller.activeFilters.isNotEmpty ||
                            //     controller.searchQuery.isNotEmpty)
                            //   TextButton(
                            //     onPressed: () => controller.resetFilters(),
                            //     child: Text('reset_search_str'.tr),
                            //   ),
                          ],
                        ),
                      ),

                    // Claims list
                    if (controller.filteredList.isNotEmpty)
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        itemCount: controller.filteredList.length,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          final claim = controller.filteredList[index];
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            child: ClaimItem(
                              agentCode: claim.agentCode,
                              agentName: claim.agentName,
                              userLevel: controller.level,
                              claimId: claim.claimId,
                              policyHolder: claim.policyHolder,
                              policyNumber: claim.policyNumber,
                              issueDate: controller.formatDate(
                                claim.requestedDate,
                              ),
                              claimAmount: claim.amount.formatCurrencyId(),
                              note: claim.remark,
                              bulletNotes:
                                  claim.diagnose.isNotEmpty
                                      ? [claim.diagnose]
                                      : [],
                              status: claim.claimStatus,
                            ),
                          );
                        },
                      ),
                  ],
                ),
            ],
          );
        }),
      ),
    );
  }
}

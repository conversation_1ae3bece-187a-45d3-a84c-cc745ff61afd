import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:pdl_superapp/controllers/main_controller.dart';
import 'package:pdl_superapp/controllers/profile/setting_controller.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:uuid/uuid.dart';

import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>/kompensasi_widget.dart';
import 'package:pdl_superapp/pages/home/<USER>/validasi_widget.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeController extends BaseControllers {
  late SharedPreferences prefs;

  // for UI
  Rx<UserModels> userData = Rx(UserModels());

  // Favorite widgets
  final RxList<String> favoriteWidgetIds = RxList();
  final RxList<Widget> favoriteWidgets = RxList();

  RxString userLevel = ''.obs;

  // Loading state for UI
  @override
  RxBool isLoading = true.obs;

  final settingC = Get.put(SettingController());
  final MainController mainC = Get.find();

  @override
  void onInit() async {
    super.onInit();
    _setLoadingState(true); // Set loading to true on initialization
    prefs = await SharedPreferences.getInstance();

    // Load user level from preferences first
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';

    // Load cached user data if available to show immediately
    await _loadCachedUserData();

    // Progressive loading: Load cached favorite widgets immediately
    // This will show cached content while other initialization happens
    await _loadFavoriteWidgetsFromCache();

    // Check if app was restarted due to Shorebird update
    final wasRestarted = prefs.getBool('shorebird_restart_flag') ?? false;
    if (wasRestarted) {
      log('App was restarted due to Shorebird update, adding extra delay');
      // Clear the flag
      await prefs.setBool('shorebird_restart_flag', false);
      // Add extra delay for iOS after Shorebird restart
      await Future.delayed(const Duration(milliseconds: 2000));
    } else {
      // Reduced delay for faster startup
      await Future.delayed(const Duration(milliseconds: 200));
    }

    // Ensure ConfigReader is initialized before making API calls
    try {
      await ConfigReader.initialize();
    } catch (e) {
      log('ConfigReader re-initialization failed: $e');
    }

    await registerDevice();
  }

  /// Load cached user data from SharedPreferences
  Future<void> _loadCachedUserData() async {
    try {
      // Load basic user info from SharedPreferences to show immediately
      String agentName = prefs.getString(kStorageAgentName) ?? '';
      String agentCode = prefs.getString(kStorageAgentCode) ?? '';
      String userLevelStr = prefs.getString(kStorageUserLevel) ?? '';
      String userId = prefs.getString(kStorageUserId) ?? '';

      if (agentName.isNotEmpty || agentCode.isNotEmpty) {
        // Create a basic UserModels object with cached data
        userData.value = UserModels(
          agentName: agentName.isNotEmpty ? agentName : null,
          agentCode: agentCode.isNotEmpty ? agentCode : null,
          level: userLevelStr.isNotEmpty ? userLevelStr : null,
          id: userId.isNotEmpty ? int.tryParse(userId) : null,
        );
        log('Loaded cached user data successfully: $agentName');
        // Don't set isLoading to false here, wait for fresh data
      }
    } catch (e) {
      log('Error loading cached user data: $e');
    }
  }

  /// Load fresh data from API in proper sequence
  Future<void> _loadFreshData() async {
    try {
      log('Starting fresh data load sequence');

      // First load profile data (this is critical for user info)
      // This is awaited to ensure user data is loaded before widgets
      await api.getProfile(
        controllers: this,
        code: kReqGetProfile,
        debug: true,
      );

      log('Profile data loaded, now loading widget sort');

      // Then load widget sort data (not awaited to allow parallel loading)
      // This will be handled by its own success/failure callbacks
      api.getWidgetSort(controllers: this);
    } catch (e) {
      log('Error loading fresh data: $e');
      // If API fails, at least show cached data
      _setLoadingState(false);
    }
  }

  @override
  void onReady() async {
    super.onReady();
    // Add additional delay for iOS after potential Shorebird restart
    if (Platform.isIOS) {
      await Future.delayed(const Duration(milliseconds: 1000));
    }

    // Check for updates after UI is fully ready
    if (!kIsWeb) {
      await Utils.checkForUpdates();
    }
  }

  loadData() async {
    try {
      // Progressive loading: Load cached data first
      await _loadFavoriteWidgetsFromCache();

      // Load fresh data from API in sequence to avoid race conditions
      await _loadFreshData();

      userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
    } catch (e) {
      log('Error in loadData: $e');
      _setLoadingState(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) async {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqAddDevices:
        // Load data after device is registered
        _setLoadingState(true);
        loadData();
        break;
      case kReqGetProfile:
        log('Profile API success, parsing data');
        // parse data user
        await parseData(response);
        // Load favorite widgets after profile is parsed
        loadFavoriteWidgets();
        // isLoading.value is set to false in parseData
        break;
      case 0: // Default code for getWidgetSort
        log('Widget sort API success');
        // Widget sort data is handled automatically by the API
        // No specific parsing needed here
        break;
      default:
        log('Unknown request code: $requestCode');
        _setLoadingState(
          false,
        ); // Ensure loading is set to false for other cases
    }
  }

  @override
  load() async {
    super.load();
    _setLoadingState(true); // Set loading to true before API call

    try {
      // Progressive loading: Load favorite widgets from cache first
      await _loadFavoriteWidgetsFromCache();

      // Load fresh data from API in sequence
      await _loadFreshData();

      // Check for favorite widget changes and reload if needed
      await checkForFavoriteWidgetChanges();

      // Force reload favorite widgets from Firestore
      await _forceReloadFavoriteWidgets();
    } catch (e) {
      log('Error in load method: $e');
      _setLoadingState(
        false,
      ); // Ensure loading is set to false if API call fails
    }
  }

  // Load favorite widgets from cache immediately for faster UI response
  Future<void> _loadFavoriteWidgetsFromCache() async {
    try {
      // Load from cache first to show UI immediately
      List<String>? cachedIds = await _loadFromFirestore();

      if (cachedIds != null && cachedIds.isNotEmpty) {
        log('Loading favorite widgets from cache for immediate display');
        _updateFavoriteWidgets(cachedIds);
        // Don't set loading to false here if we're still loading fresh data
        // Only set to false if this is the only data we have
        if (userData.value.agentName != null &&
            userData.value.agentName!.isNotEmpty) {
          _setLoadingState(false);
        }
      } else {
        // If no cached data, try SharedPreferences as fallback
        log('No Firestore cache, trying SharedPreferences');
        List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];
        if (savedIds.isNotEmpty) {
          log('Using SharedPreferences favorite widgets');
          _updateFavoriteWidgets(savedIds);
        } else {
          // Last resort: use default widgets
          log('No saved favorites, using defaults');
          _loadDefaultFavoriteWidgets();
        }
      }
    } catch (e) {
      log('Error loading favorite widgets from cache: $e');
      // Fallback to default widgets on error
      _loadDefaultFavoriteWidgets();
    }
  }

  /// Centralized method to set loading state consistently
  void _setLoadingState(bool loading) {
    isLoading.value = loading;
    setLoading(loading);
  }

  // Force reload favorite widgets from Firestore
  Future<void> _forceReloadFavoriteWidgets() async {
    try {
      // Clear the last known list to force a reload
      _lastKnownFavoriteIds.clear();

      // Load favorite widgets
      loadFavoriteWidgets();
    } catch (e) {
      log('Error reloading favorite widgets: $e');
    }
  }

  // Store the last known list of favorite widget IDs for comparison
  List<String> _lastKnownFavoriteIds = [];

  // Check if favorite widgets have changed in SharedPreferences
  Future<void> checkForFavoriteWidgetChanges() async {
    try {
      List<String> currentIds = prefs.getStringList('favorite_widgets') ?? [];

      log(
        'HomeController checking favorite widget changes: last known: $_lastKnownFavoriteIds, current: $currentIds',
      );

      // Check if the list has changed
      if (!_areListsEqual(_lastKnownFavoriteIds, currentIds)) {
        log('HomeController detected favorite widget changes, reloading');
        // Update the last known list
        _lastKnownFavoriteIds = List.from(currentIds);

        // Reload the widgets
        loadFavoriteWidgets();
      } else {
        log('HomeController: No changes in favorite widgets');
      }
    } catch (e) {
      log('Error checking for favorite widget changes: $e');
    }
  }

  // Helper method to compare two lists
  bool _areListsEqual(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  // Load favorite widgets from Firestore and SharedPreferences
  void loadFavoriteWidgets() async {
    try {
      // First try to get from Firestore
      List<String>? firestoreIds = await _loadFromFirestore();

      // If Firestore data is available, use it
      if (firestoreIds != null && firestoreIds.isNotEmpty) {
        log('Using favorite widgets from Firestore');

        // Also update SharedPreferences to keep them in sync
        await prefs.setStringList('favorite_widgets', firestoreIds);

        _updateFavoriteWidgets(firestoreIds);
        return;
      }

      // Otherwise, fall back to SharedPreferences
      log('Falling back to SharedPreferences for favorite widgets');
      List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];

      // If no saved favorites, use default (first 4 widgets)
      if (savedIds.isEmpty) {
        savedIds = (prefs.getStringList(kStorageUserRoles)) ?? [];
        // Save defaults to SharedPreferences
        await prefs.setStringList('favorite_widgets', savedIds);
      }

      // Update widgets with the local data
      _updateFavoriteWidgets(savedIds);
    } catch (e) {
      log('Error loading favorite widgets: $e');

      // In case of error, try to use local data
      List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];
      if (savedIds.isEmpty) {
        savedIds = ['1', '2', '3', '4']; // Default to first 4 widgets

        // Save defaults to SharedPreferences
        await prefs.setStringList('favorite_widgets', savedIds);
      }
      _updateFavoriteWidgets(savedIds);
    }
  }

  // Update favorite widgets with the given IDs
  void _updateFavoriteWidgets(List<String> ids) {
    // Update the last known list
    _lastKnownFavoriteIds = List.from(ids);

    // Update the favoriteWidgetIds list
    favoriteWidgetIds.value = ids;

    // Generate widgets based on IDs
    generateFavoriteWidgets();
  }

  // Load favorite widgets from Firestore
  Future<List<String>?> _loadFromFirestore() async {
    try {
      // Get FirestoreServices instance
      FirestoreServices firestoreServices;
      try {
        firestoreServices = Get.find<FirestoreServices>();
      } catch (e) {
        log('FirestoreServices not found, creating new instance');
        firestoreServices = Get.put(FirestoreServices());
      }

      // Get favorite widgets from Firestore
      return await firestoreServices.getFavoriteWidgets();
    } catch (e) {
      log('Error loading from Firestore: $e');
      return null;
    }
  }

  // Generate HomeWidgetBase widgets based on favoriteWidgetIds
  void generateFavoriteWidgets() {
    favoriteWidgets.clear();

    // Map of widget IDs to their corresponding widgets
    Map<String, Widget> widgetMap = {
      '1': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-ultah-nasabah.svg',
        title: 'customer_birthday_str',
        widgetKey: kWidgetKeyUlangTahunNasabah,
        content: BirthdayWidget(),
      ),
      '2': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-status-klaim.svg',
        title: 'claim_status_str',
        widgetKey: kWidgetKeyStatusKlaim,
        content: ClaimWidget(),
      ),
      '3': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-polis-lapse.svg',
        title: 'lapsed_policy_str',
        widgetKey: kWidgetKeyPolisLapse,
        content: PolisLapsedWidget(),
      ),
      '4': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-polis jatuh-tempo.svg',
        title: 'expired_policy_str',
        widgetKey: kWidgetKeyPolisJatuhTempo,
        content: PolisJatuhTempoWidget(),
      ),
      '5': HomeWidgetBase(
        iconUrl: 'icon/widget-produksi-saya.svg',
        title: 'my_production_str',
        widgetKey: kWidgetKeyProduksiSaya,
        content: HomeWidgetProduction(),
      ),
      '6': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-validasi.svg',
        title: 'validation_promotion_str',
        widgetKey: kWidgetKeyValidasiPromosi,
        content: ValidasiWidget(),
      ),
      '7': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-estimasi.svg',
        title: 'compensation_estimate_str',
        widgetKey: kWidgetKeyEstimasiKompensasi,
        content: KompensasiWidget(),
      ),
      '8': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-persistensi.svg',
        title: 'persistency_str',
        widgetKey: kWidgetKeyPersistensi,
        content: PersistensiWidget(),
      ),
      '9': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-spaj.svg',
        title: 'status_spaj_str',
        widgetKey: kWidgetKeyStatusSpaj,
        content: HomeWidgetSpaj(),
      ),
    };

    // Add widgets in the order specified by favoriteWidgetIds
    log('Generating ${favoriteWidgetIds.length} favorite widgets');
    for (String id in favoriteWidgetIds) {
      if (widgetMap.containsKey(id)) {
        log('Adding widget with ID: $id');
        favoriteWidgets.add(widgetMap[id]!);
      }
    }
  }

  parseData(response) async {
    try {
      UserModels data = UserModels.fromJson(response);
      userData.value = data;
      userLevel.value = data.roles?.code ?? '-';

      // Save user data to SharedPreferences for caching
      await prefs.setString(kStorageAgentName, data.agentName ?? '-');
      await prefs.setString(kStorageUserLevel, data.level ?? '-');
      await prefs.setString(kStorageUserLevelComplete, data.roles?.code ?? '-');
      await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
      await prefs.setString(
        kStorageAgentCode,
        data.agentCode ?? data.username ?? '-',
      );

      var widgetAccess =
          data.roles?.accesses
              ?.where((element) {
                return element.domain?.contains("agent.Widget") ?? false;
              })
              .where((element) {
                return element.action?.contains("view") ?? false;
              }) ??
          [];
      await prefs.setStringList(
        kStorageUserRoles,
        widgetAccess.map((e) {
          var domain = e.domain ?? "";
          if (domain.contains("agent.Widget.BirthdayCustomer")) {
            return '1';
          } else if (domain.contains("agent.Widget.ClaimTracking")) {
            return '2';
          } else if (domain.contains("agent.Widget.PolicyLapsed")) {
            return '3';
          } else if (domain.contains("agent.Widget.PolicyOverdue")) {
            return '4';
          } else if (domain.contains("agent.Widget.Produksi")) {
            return '5';
          } else if (domain.contains("agent.Widget.Promosi")) {
            return '6';
          } else if (domain.contains("agent.Widget.Commission")) {
            return '7';
          } else if (domain.contains("agent.Widget.Persistency")) {
            return '8';
          } else if (domain.contains("agent.Widget.Spaj")) {
            return '9';
          } else {
            return '';
          }
        }).toList(),
      );

      if (mainC.isLangCodeNotNull.value) {
        updateLocaleFromLogin();
      }

      log('User data parsed and cached successfully: ${data.agentName}');
    } catch (e) {
      log('Error parsing user data: $e');
    } finally {
      _setLoadingState(
        false,
      ); // Ensure loading is set to false even if parsing fails
    }
  }

  void updateLocaleFromLogin() {
    settingC.onChangeTempLocal(mainC.langCode.value!);
    settingC.setLocale(
      fId: 'agents-${userData.value.agentCode}-${userData.value.id}',
    );
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    log(
      'Load failed for request code: $requestCode, response: ${response.statusText}',
    );

    // Handle specific failures
    switch (requestCode) {
      case kReqGetProfile:
        log('Profile API failed, keeping cached data if available');
        break;
      case 0: // getWidgetSort
        log('Widget sort API failed, using default widget order');
        break;
      default:
        log('Unknown API failed: $requestCode');
    }

    _setLoadingState(false); // Ensure loading is set to false on failure
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);
    log('Load error: $e');

    // Implement fallback mechanisms
    _handleLoadError(e, response);

    _setLoadingState(false); // Ensure loading is set to false on error
  }

  /// Handle load errors with fallback mechanisms
  void _handleLoadError(dynamic error, Response? response) {
    log('Handling load error with fallback mechanisms');

    // If we have cached user data, keep showing it
    if (userData.value.agentName != null &&
        userData.value.agentName!.isNotEmpty) {
      log('Using cached user data as fallback');
    } else {
      // Try to load from SharedPreferences as last resort
      _loadCachedUserData();
    }

    // If we have cached favorite widgets, keep showing them
    if (favoriteWidgets.isNotEmpty) {
      log('Using cached favorite widgets as fallback');
    } else {
      // Try to load default widgets
      _loadDefaultFavoriteWidgets();
    }
  }

  /// Load default favorite widgets when all else fails
  void _loadDefaultFavoriteWidgets() {
    try {
      log('Loading default favorite widgets as fallback');
      List<String> defaultIds = ['1', '2', '3', '4']; // Default first 4 widgets
      _updateFavoriteWidgets(defaultIds);
    } catch (e) {
      log('Error loading default favorite widgets: $e');
    }
  }

  performAddDevice(var data) async {
    await api.performRegisterDevice(
      controllers: this,
      data: data,
      code: kReqAddDevices,
    );
  }

  registerDevice() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String buildNumber = await Utils.getBuildNumber();
    String appVersion = await Utils.getAppVersionOnly();
    String deviceId = '';

    // Get Firebase token from NotificationService
    String firebaseToken = '';
    try {
      firebaseToken = await FirebaseMessaging.instance.getToken() ?? '';
    } catch (e) {
      log('Error getting Firebase token: $e');
    }

    var data = {};
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfoPlugin.webBrowserInfo;

      // Generate or retrieve UUID for web device ID
      String? storedWebDeviceId = prefs.getString(kWebDeviceId);
      if (storedWebDeviceId == null || storedWebDeviceId.isEmpty) {
        // Generate new UUID and store it
        storedWebDeviceId = const Uuid().v4();
        await prefs.setString(kWebDeviceId, storedWebDeviceId);
      }
      deviceId = storedWebDeviceId;

      data = {
        "deviceId": storedWebDeviceId,
        "deviceModel": "${webBrowserInfo.browserName}",
        "osType": "${webBrowserInfo.platform}",
        "osVersion": "${webBrowserInfo.appVersion}",
        "appVersion": appVersion,
        "appBuildNumber": buildNumber,
        "deviceLanguage": "${Get.locale}",
        "screenWidth": Get.width,
        "screenHeight": Get.height,
        "connectionType": "",
        "timezone": "",
        "firebaseToken": firebaseToken,
        "manufacturer": "${webBrowserInfo.vendor}",
      };
    } else {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = androidInfo.id;
        data = {
          "deviceId": androidInfo.id,
          "deviceModel": androidInfo.model,
          "osType": Platform.localeName,
          "osVersion": androidInfo.version.release,
          "appVersion": appVersion,
          "appBuildNumber": buildNumber,
          "deviceLanguage": "${Get.locale}",
          "screenWidth": Get.width,
          "screenHeight": Get.height,
          "connectionType": "",
          "timezone": "",
          "firebaseToken": firebaseToken,
          "manufacturer": androidInfo.manufacturer,
        };
      }
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = '${iosInfo.identifierForVendor}';
        data = {
          "deviceId": "${iosInfo.identifierForVendor}",
          "deviceModel": iosInfo.modelName,
          "osType": Platform.localeName,
          "osVersion": iosInfo.systemVersion,
          "appVersion": appVersion,
          "appBuildNumber": buildNumber,
          "deviceLanguage": "${Get.locale}",
          "screenWidth": Get.width,
          "screenHeight": Get.height,
          "connectionType": "",
          "timezone": "",
          "firebaseToken": firebaseToken,
          "manufacturer": "Apple",
        };
      }
    }

    performAddDevice(data);
    await prefs.setString(kDeviceId, deviceId);
  }

  void changeTheme(String name, ThemeData theme) {
    Get.changeTheme(theme);
  }
}

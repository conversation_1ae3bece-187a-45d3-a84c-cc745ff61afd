server {
    listen 8083;
    server_name localhost;

    root /usr/share/nginx/html;
    index index.html;

    # Serve .well-known files directly (for deep linking, app verification, etc.)
    location /.well-known/ {
        try_files $uri =404;
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin *;
        expires 1d;
        access_log off;
    }

    # Serve static assets with proper MIME types and caching
    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff2?|eot|ttf|svg|wasm)$ {
        try_files $uri =404;
        expires 6M;
        access_log off;
        add_header Cache-Control "public";
    }

    # All other requests go to Flutter app
    location / {
        try_files $uri $uri/ /index.html;
    }

    error_page 404 /index.html;
}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/widget/kompensasi_page_controller.dart';
import 'package:pdl_superapp/models/kompensasi_item_model.dart';
import 'package:pdl_superapp/utils/constants.dart';

class KompensasiPage extends StatelessWidget {
  KompensasiPage({super.key});

  final KompensasiPageController controller = Get.put(
    KompensasiPageController(),
    tag: "kompensasi-page-${DateTime.timestamp()}",
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'compensation_estimate_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          if (controller.isLoading.value ||
              controller.kompensasiController.isLoading.isTrue) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(paddingLarge),
                child: CircularProgressIndicator(),
              ),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: paddingMedium,
            children: [
              const SizedBox(height: paddingSmall),
              TitleWidget(title: 'period_str'.tr),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radiusSmall),
                  color: Get.isDarkMode ? kColorGlobalBgDarkBlue : kLine,
                ),
                padding: const EdgeInsets.all(paddingSmall),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      controller.kompensasiController.periode.value,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(width: paddingSmall),
                    Text(
                      '(${controller.kompensasiController.periodeTanggal.value})',
                    ),
                  ],
                ),
              ),
              // TableCard with yearly commission data
              TableKomisiDasar(
                level: controller.level,
                data: controller.yearlyData,
              ),

              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(radiusSmall),
                ),
                padding: const EdgeInsets.all(paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'current_commision_estimate'.tr,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: paddingSmall),
                    // Dynamic Table
                    Obx(() {
                      if (controller.kompensasiController.isLoading.isTrue) {
                        return Center(child: CircularProgressIndicator());
                      }

                      return Table(
                        columnWidths: const {
                          0: FlexColumnWidth(2),
                          1: FlexColumnWidth(1),
                        },
                        children: [
                          // Generate rows for each item
                          ...controller.kompensasiController.items.map(
                            (item) => _buildTableRow(item),
                          ),
                          // Total row
                          _buildTableRow(
                            KompensasiItemModel(
                              label: 'Total',
                              value:
                                  controller
                                      .kompensasiController
                                      .totalValue
                                      .value,
                              isTotal: true,
                            ),
                          ),
                        ],
                      );
                    }),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  TableRow _buildTableRow(KompensasiItemModel item) {
    bool showing = controller.isShowingField(item.label);
    return TableRow(
      children: [
        (showing)
            ? Padding(
              padding: const EdgeInsets.only(top: paddingSmall),
              child: Text(
                item.label,
                style: TextStyle(
                  fontWeight:
                      item.isTotal ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            )
            : SizedBox.shrink(),
        (showing)
            ? Padding(
              padding: const EdgeInsets.only(top: paddingSmall),
              child: Text(
                item.value,
                textAlign: TextAlign.end,
                style: TextStyle(
                  fontWeight:
                      item.isTotal ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            )
            : SizedBox.shrink(),
      ],
    );
  }
}

class TableKomisiDasar extends StatelessWidget {
  const TableKomisiDasar({super.key, required this.level, required this.data});
  final String level;
  final List<List<String>> data;

  @override
  Widget build(BuildContext context) {
    Color? contentColor;
    Color? bgColorValueGanjil;
    Color? bgColorValueGenap;
    Color? border;
    bool isDataMoreThan2 = (data.isEmpty) ? false : data.first.length > 2;

    if (Get.isDarkMode) {
      contentColor = kColorTextDark;
      bgColorValueGanjil = kColorGlobalBgDarkBlue;
      bgColorValueGenap = Colors.transparent;
      border = kColorBorderDark;
    } else {
      contentColor = kColorTextLight;
      bgColorValueGanjil = kLine;
      bgColorValueGenap = Colors.transparent;
      border = kColorBorderLight;
    }
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: border),
        borderRadius: BorderRadius.all(Radius.circular(15)),
      ),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: kColorPaninBlue,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            padding: EdgeInsets.symmetric(vertical: paddingMedium),
            child: Row(
              children: [
                if (level == 'BM' || level == 'BD')
                  Expanded(
                    child: Text(
                      '',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'basic_commission'.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                if (level == 'BM' || level == 'BD' && isDataMoreThan2)
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Override',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
          ListView.builder(
            padding: EdgeInsets.zero,
            physics: NeverScrollableScrollPhysics(),
            itemCount: data.length,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              bool isLast = index == (data.length - 1);
              final row = data[index];

              if (isLast) {
                return Container(
                  padding: EdgeInsets.symmetric(vertical: paddingMedium),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(15),
                      bottomRight: Radius.circular(15),
                    ),
                    color:
                        index % 2 == 0 ? bgColorValueGanjil : bgColorValueGenap,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          row[0],
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: contentColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Expanded(
                        flex: 4,
                        child: Text(
                          row[1],
                          style: TextStyle(fontSize: 14, color: contentColor),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Container(
                padding: EdgeInsets.symmetric(vertical: paddingMedium),
                decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: border!)),
                  color:
                      index % 2 == 0 ? bgColorValueGanjil : bgColorValueGenap,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        row[0],
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: contentColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: (level == 'BM' || level == 'BD') ? 2 : 3,
                      child: Text(
                        row[1],
                        style: TextStyle(fontSize: 14, color: contentColor),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    if ((level == 'BM' || level == 'BD') && row.length > 2)
                      Expanded(
                        flex: 2,
                        child: Text(
                          row[2],
                          style: TextStyle(fontSize: 14, color: contentColor),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

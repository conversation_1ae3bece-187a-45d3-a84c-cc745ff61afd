import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pd_radio_button.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_form_identification_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PublicFormIdentification extends StatelessWidget {
  final PublicFormIdentificationController controller;

  const PublicFormIdentification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _ktpSection(context),
        SizedBox(height: paddingMedium),
        _addressKtpSection(context),
        SizedBox(height: paddingMedium),
        TitleWidget(title: 'Alamat Tempat Tinggal Saat Ini'),
        Container(
          padding: EdgeInsets.symmetric(vertical: paddingSmall),
          child: Obx(
            () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PdlRadioButton(
                  index: 0,
                  selectedIndex: controller.selectedIsAddressSame.value,
                  onTap: () {
                    controller.alamatSameAsKtpSelectedError.value = '';
                    controller.selectedIsAddressSame.value = 0;
                  },
                  title: 'Sama dengan KTP',
                ),
                SizedBox(height: paddingSmall),
                PdlRadioButton(
                  index: 1,
                  selectedIndex: controller.selectedIsAddressSame.value,
                  onTap: () {
                    controller.alamatSameAsKtpSelectedError.value = '';
                    controller.selectedIsAddressSame.value = 1;
                  },
                  title: 'Beda dengan KTP',
                ),
                if (controller.alamatSameAsKtpSelectedError.value != '')
                  Text(
                    controller.alamatSameAsKtpSelectedError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
              ],
            ),
          ),
        ),
        Obx(() {
          if (controller.selectedIsAddressSame.value == 3) {
            return SizedBox();
          } else {
            return _addressKtpSection(context, isHomeAddress: true);
          }
        }),
      ],
    );
  }

  Column _addressKtpSection(BuildContext context, {bool? isHomeAddress}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isHomeAddress != true) TitleWidget(title: 'Alamat Sesuai KTP'),
        Text(
          isHomeAddress == true
              ? 'Masukan alamat tempat tinggal saat ini'
              : 'pastikan data berikut sama persis dengan KTP.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Provinsi',
              isCapslockOn: true,
              textController:
                  isHomeAddress == true
                      ? controller.provinsiDomisiliController
                      : controller.provinsiKtpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    isHomeAddress == true
                        ? controller.provinsiDomisiliController
                        : controller.provinsiKtpController,
                  ),
              hasError:
                  isHomeAddress == true
                      ? controller.provinsiDomisiliError.value.isNotEmpty
                      : controller.provinsiKtpError.value.isNotEmpty,
              errorText:
                  isHomeAddress == true
                      ? controller.provinsiDomisiliError.value.isEmpty
                          ? null
                          : controller.provinsiDomisiliError.value
                      : controller.provinsiKtpError.value.isEmpty
                      ? null
                      : controller.provinsiKtpError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Kabupaten / Kota',
              isCapslockOn: true,
              textController:
                  isHomeAddress == true
                      ? controller.kabupatenDomisiliController
                      : controller.kabupatenKtpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    isHomeAddress == true
                        ? controller.kabupatenDomisiliController
                        : controller.kabupatenKtpController,
                  ),
              hasError:
                  isHomeAddress == true
                      ? controller.kabupatenDomisiliError.value.isNotEmpty
                      : controller.kabupatenKtpError.value.isNotEmpty,
              errorText:
                  isHomeAddress == true
                      ? controller.kabupatenDomisiliError.value.isEmpty
                          ? null
                          : controller.kabupatenDomisiliError.value
                      : controller.kabupatenKtpError.value.isEmpty
                      ? null
                      : controller.kabupatenKtpError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Kelurahan / Desa',
              isCapslockOn: true,
              textController:
                  isHomeAddress == true
                      ? controller.kelurahanDomisiliController
                      : controller.kelurahanKtpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    isHomeAddress == true
                        ? controller.kelurahanDomisiliController
                        : controller.kelurahanKtpController,
                  ),
              hasError:
                  isHomeAddress == true
                      ? controller.kelurahanDomisiliError.value.isNotEmpty
                      : controller.kelurahanKtpError.value.isNotEmpty,
              errorText:
                  isHomeAddress == true
                      ? controller.kelurahanDomisiliError.value.isEmpty
                          ? null
                          : controller.kelurahanDomisiliError.value
                      : controller.kelurahanKtpError.value.isEmpty
                      ? null
                      : controller.kelurahanKtpError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Kecamatan',
              isCapslockOn: true,
              textController:
                  isHomeAddress == true
                      ? controller.kecamatanDomisiliController
                      : controller.kecamatanKtpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    isHomeAddress == true
                        ? controller.kecamatanDomisiliController
                        : controller.kecamatanKtpController,
                  ),
              hasError:
                  isHomeAddress == true
                      ? controller.kecamatanDomisiliError.value.isNotEmpty
                      : controller.kecamatanKtpError.value.isNotEmpty,
              errorText:
                  isHomeAddress == true
                      ? controller.kecamatanDomisiliError.value.isEmpty
                          ? null
                          : controller.kecamatanDomisiliError.value
                      : controller.kecamatanKtpError.value.isEmpty
                      ? null
                      : controller.kecamatanKtpError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Alamat',
              isCapslockOn: true,
              textController:
                  isHomeAddress == true
                      ? controller.alamatDomisiliController
                      : controller.alamatKtpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    isHomeAddress == true
                        ? controller.alamatDomisiliController
                        : controller.alamatKtpController,
                  ),
              hasError:
                  isHomeAddress == true
                      ? controller.alamatDomisiliError.value.isNotEmpty
                      : controller.alamatKtpError.value.isNotEmpty,
              errorText:
                  isHomeAddress == true
                      ? controller.alamatDomisiliError.value.isEmpty
                          ? null
                          : controller.alamatDomisiliError.value
                      : controller.alamatKtpError.value.isEmpty
                      ? null
                      : controller.alamatKtpError.value,
            ),
          ),
        ),
        _padding(
          Row(
            children: [
              Expanded(
                child: Obx(
                  () => PdlTextField(
                    label: 'RT',
                    keyboardType: TextInputType.number,
                    textController:
                        isHomeAddress == true
                            ? controller.rtDomisiliController
                            : controller.rtKtpController,
                    onEditingComplete:
                        () => controller.trimTextFieldOnComplete(
                          isHomeAddress == true
                              ? controller.rtDomisiliController
                              : controller.rtKtpController,
                        ),
                    hasError:
                        isHomeAddress == true
                            ? controller.rtDomisiliError.value.isNotEmpty
                            : controller.rtKtpError.value.isNotEmpty,
                    errorText:
                        isHomeAddress == true
                            ? controller.rtDomisiliError.value.isEmpty
                                ? null
                                : controller.rtDomisiliError.value
                            : controller.rtKtpError.value.isEmpty
                            ? null
                            : controller.rtKtpError.value,
                  ),
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: Obx(
                  () => PdlTextField(
                    label: 'RW',
                    keyboardType: TextInputType.number,
                    textController:
                        isHomeAddress == true
                            ? controller.rwDomisiliController
                            : controller.rwKtpController,
                    onEditingComplete:
                        () => controller.trimTextFieldOnComplete(
                          isHomeAddress == true
                              ? controller.rwDomisiliController
                              : controller.rwKtpController,
                        ),
                    hasError:
                        isHomeAddress == true
                            ? controller.rwDomisiliError.value.isNotEmpty
                            : controller.rwKtpError.value.isNotEmpty,
                    errorText:
                        isHomeAddress == true
                            ? controller.rwDomisiliError.value.isEmpty
                                ? null
                                : controller.rwDomisiliError.value
                            : controller.rwKtpError.value.isEmpty
                            ? null
                            : controller.rwKtpError.value,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Column _ktpSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Data Diri Sesuai KTP'),
        Text(
          'Mohon mengisi data berikut sama persis dengan KTP.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'NIK',
              textController: controller.nikController,
              keyboardType: TextInputType.number,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.nikController,
                  ),
              hasError: controller.nikError.value.isNotEmpty,
              errorText:
                  controller.nikError.value.isEmpty
                      ? null
                      : controller.nikError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Nama Lengkap Sesuai KTP',
              isCapslockOn: true,
              textController: controller.namaKtpController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.namaKtpController,
                  ),
              hasError: controller.namaKtpError.value.isNotEmpty,
              errorText:
                  controller.namaKtpError.value.isEmpty
                      ? null
                      : controller.namaKtpError.value,
            ),
          ),
        ),
        _padding(
          Obx(
            () => PdlTextField(
              label: 'Tempat Lahir',
              isCapslockOn: true,
              textController: controller.tempatLahirController,
              onEditingComplete:
                  () => controller.trimTextFieldOnComplete(
                    controller.tempatLahirController,
                  ),
              hasError: controller.tempatLahirError.value.isNotEmpty,
              errorText:
                  controller.tempatLahirError.value.isEmpty
                      ? null
                      : controller.tempatLahirError.value,
            ),
          ),
        ),
        _padding(
          Row(
            children: [
              Expanded(
                child: Obx(
                  () => PdlTextField(
                    label: 'Tanggal',
                    keyboardType: TextInputType.number,
                    textController: controller.tanggalLahirController,
                    onEditingComplete:
                        () => controller.trimTextFieldOnComplete(
                          controller.tanggalLahirController,
                        ),
                    hasError: controller.tanggalLahirError.value.isNotEmpty,
                    errorText:
                        controller.tanggalLahirError.value.isEmpty
                            ? null
                            : controller.tanggalLahirError.value,
                  ),
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: Obx(
                  () => PdlDropDown(
                    item: controller.monthList,
                    title: 'Bulan',
                    selectedItem:
                        controller.bulanLahirController.text.isEmpty
                            ? null
                            : controller.bulanLahirController.text,
                    disableSearch: true,
                    onChanged:
                        (val) =>
                            controller.bulanLahirController.text = val ?? '',
                    enabled: true,
                    hasError: controller.bulanLahirError.value.isNotEmpty,
                    errorText:
                        controller.bulanLahirError.value.isEmpty
                            ? null
                            : controller.bulanLahirError.value,
                  ),
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: Obx(
                  () => PdlTextField(
                    label: 'Tahun',
                    keyboardType: TextInputType.number,
                    textController: controller.tahunLahirController,
                    onEditingComplete:
                        () => controller.trimTextFieldOnComplete(
                          controller.tahunLahirController,
                        ),
                    hasError: controller.tahunLahirError.value.isNotEmpty,
                    errorText:
                        controller.tahunLahirError.value.isEmpty
                            ? null
                            : controller.tahunLahirError.value,
                  ),
                ),
              ),
            ],
          ),
        ),
        _padding(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Jenis Kelamin',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: paddingSmall),
              Obx(
                () => Wrap(
                  spacing: paddingMedium,
                  children: [
                    PdlRadioButton(
                      index: 0,
                      selectedIndex: controller.selectedJenisKelamin.value,
                      title: 'Laki-laki',
                      onTap: () {
                        controller.selectedJenisKelamin.value = 0;
                        controller.jenisKelaminController.text = 'Laki-laki';
                      },
                    ),
                    PdlRadioButton(
                      index: 1,
                      selectedIndex: controller.selectedJenisKelamin.value,
                      title: 'Perempuan',
                      onTap: () {
                        controller.selectedJenisKelamin.value = 1;
                        controller.jenisKelaminController.text = 'Perempuan';
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        _padding(
          PdlDropDown(
            item: controller.maritalStatusList,
            selectedItem:
                controller.selectedMaritalStatus.value == ''
                    ? null
                    : controller.selectedMaritalStatus.value.replaceAll(
                      '_',
                      ' ',
                    ),
            title: 'Status Perkawinan',
            onChanged: (val) {
              if (val != null) {
                controller.selectedMaritalStatus.value = val.replaceAll(
                  '_',
                  ' ',
                );
              }
            },
            disableSearch: true,
            enabled: true,
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }
}

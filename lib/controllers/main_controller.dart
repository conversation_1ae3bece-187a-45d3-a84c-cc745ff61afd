// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/tutor_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/keys.dart';

class MainController extends GetxController {
  final langCode = Rxn<String>();
  RxBool isLangCodeNotNull = false.obs;

  late SharedPreferences prefs;
  final RxInt selectedIndex = 0.obs;

  // declare global key for tutorial use
  final GlobalKey _keyShowNotif = GlobalKey();
  final GlobalKey _keyShowTugas = GlobalKey();
  final GlobalKey _keyShowCard = GlobalKey();
  final GlobalKey _keyShowMenu = GlobalKey();
  final GlobalKey _keyShowFavWidget = GlobalKey();
  final GlobalKey _keyShowTabBar = GlobalKey();

  List<TutorModel> tutorBeranda = [];

  @override
  Future<void> onInit() async {
    super.onInit();
    _assignTutorBeranda();

    prefs = await SharedPreferences.getInstance();
    _checkIsNeedChangePass();
    _getArguments();
  }

  void _getArguments() {
    if (Get.arguments != null) {
      if (Get.arguments['language'] != null) {
        langCode.value = Get.arguments['language'];
        isLangCodeNotNull.value = langCode.value != null;
      }

      if (Get.arguments['index'] != null) {
        selectedIndex.value = Get.arguments['index'];

        if (Get.arguments['start_tutorial'] == true) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            switch (selectedIndex.value) {
              case 0:
                _startBerandaTutorial();
                break;
              default:
            }
          });
        }
      }
    }
  }

  void _assignTutorBeranda() {
    tutorBeranda = [
      TutorModel(
        number: 1,
        key: _keyShowNotif,
        title: 'notification_str'.tr,
        description: 'desc_tutor_notif_str'.tr,
      ),
      TutorModel(
        number: 2,
        key: _keyShowTugas,
        title: 'my_task_str'.tr,
        description: 'desc_tutor_task_status'.tr,
      ),
      TutorModel(
        number: 3,
        key: _keyShowCard,
        title: 'private_data_information_str'.tr,
        description: 'desc_private_data_information_str'.tr,
      ),
      TutorModel(
        number: 4,
        key: _keyShowMenu,
        title: 'menu_str'.tr,
        description: 'desc_tutor_main_menu'.tr,
      ),
      TutorModel(
        number: 5,
        key: _keyShowFavWidget,
        title: 'fave_widget_str'.tr,
        description: 'desc_tutor_fav_widget'.tr,
      ),
      TutorModel(
        number: 6,
        key: _keyShowTabBar,
        title: 'navbar_str'.tr,
        description: 'desc_tutor_navbar'.tr,
      ),
    ];
  }

  void _checkIsNeedChangePass() {
    final isNeedChangePass = prefs.getBool(kStorageNeedChangePass);
    if (isNeedChangePass ?? false) {
      Get.offAllNamed(Routes.FIRST_LOGIN_SUCCESS);
    }
  }

  void _startBerandaTutorial() {
    if (showCaseContext != null) {
      final keys = tutorBeranda.map((e) => e.key).toList();
      ShowCaseWidget.of(showCaseContext!).startShowCase(keys);
    }
  }
}

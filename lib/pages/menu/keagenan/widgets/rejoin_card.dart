import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/models/response/list_approval_response.dart';
import 'package:pdl_superapp/models/response/requested_rejoin_response.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RejoinCard extends StatelessWidget {
  const RejoinCard({super.key, required this.data});
  final ApprovalDataModel data;

  @override
  Widget build(BuildContext context) {
    final requestBy = RequestedRejoinModel.fromJson(data.detailData);
    return GestureDetector(
      onTap:
          () => Get.toNamed(
            Routes.DETAIL_REJOIN,
            arguments: {
              'id_rejoin': requestBy.id,
              'id_approval_header': data.id,
              'as_approver': true,
            },
          ),
      child: Container(
        width: Get.width,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          bottom: paddingMedium,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      PdlCircleAvatar(
                        source: requestBy.agentPicture ?? '',
                        border: true,
                        height: 44,
                        width: 44,
                      ),

                      SizedBox(width: paddingMedium),
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Bergabung Kembali'),
                                  SizedBox(height: paddingSmall),
                                  Text(
                                    '${requestBy.proposedLevel} ${requestBy.agentName}',
                                    style: Theme.of(context).textTheme.bodyLarge
                                        ?.copyWith(fontWeight: FontWeight.w700),
                                  ),
                                  SizedBox(height: paddingSmall),
                                  Text(
                                    '${requestBy.branch?.branchName} - ${requestBy.agentCode}',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(fontWeight: FontWeight.w700),
                                  ),
                                  SizedBox(height: paddingMedium),
                                  StatusLabel(status: requestBy.status ?? ''),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: paddingSmall),
                  Divider(),
                ],
              ),
            ),
            Utils.cachedSvgWrapper('icon/ic-arrow-right-circle.svg'),
          ],
        ),
      ),
    );
  }
}

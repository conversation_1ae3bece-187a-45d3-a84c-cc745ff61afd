import 'dart:io';

import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingController extends BaseControllers {
  RxString locale = 'id_ID'.obs;
  RxString tempLocale = 'id_ID'.obs;

  RxBool themeIsDark = false.obs;
  RxBool tempThemeIsDark = false.obs;

  RxBool loggingEnabled = true.obs;

  @override
  void onInit() {
    super.onInit();
    locale.value = Get.locale.toString();
    tempLocale.value = Get.locale.toString();

    themeIsDark.value = Get.isDarkMode;
    tempThemeIsDark.value = Get.isDarkMode;

    // Initialize logging state
    try {
      final loggerService = Get.find<LoggerService>();
      loggingEnabled.value = loggerService.isLoggingEnabled;
    } catch (e) {
      // If LoggerService is not initialized yet, default to true
      loggingEnabled.value = true;
    }
  }

  //locale
  setLocale({String? fId}) {
    Utils.setLocale(tempLocale.value);
    Get.back();

    locale.value = Get.locale.toString();
    tempLocale.value = locale.value;

    // setFireStore
    FirestoreServices().setLanguage(tempLocale.value, fId: fId);
  }

  onChangeTempLocal(String localeId) {
    tempLocale.value = localeId;
  }

  onChangeTempTheme(bool isDark) {
    tempThemeIsDark.value = isDark;
  }

  // theme
  setTheme() {
    bool val = false;
    val = Utils.setTheme(tempThemeIsDark.value);
    Get.back();

    themeIsDark.value = val;
    tempThemeIsDark.value = val;

    // setFireStore
    FirestoreServices().setTheme(themeIsDark.value);
  }

  updateApps() {
    if (Platform.isAndroid || Platform.isIOS) {
      final appId =
          Platform.isAndroid ? 'YOUR_ANDROID_PACKAGE_ID' : 'YOUR_IOS_APP_ID';
      final url = Uri.parse(
        Platform.isAndroid
            ? "market://details?id=$appId"
            : "https://apps.apple.com/app/id$appId",
      );
      launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  // Toggle logging
  Future<void> toggleLogging() async {
    try {
      final loggerService = Get.find<LoggerService>();
      final newState = await loggerService.toggleLogging();
      loggingEnabled.value = newState;
    } catch (e) {
      // Handle error
      // Get.snackbar('Error', 'Failed to toggle logging');
    }
  }
}

- name: Deploy New ECS Task Definition
  hosts: localhost
  tasks:
    - name: Get current ECS task definition
      command: >
        aws ecs describe-task-definition --task-definition {{ task_family }} --query 'taskDefinition' --output json
      register: current_task_output

    - name: Extract required fields from task definition
      set_fact:
        new_task_def: >-
          {{
            {
              "family": current_task_output.stdout | from_json | json_query('family'),
              "taskRoleArn": current_task_output.stdout | from_json | json_query('taskRoleArn'),
              "executionRoleArn": current_task_output.stdout | from_json | json_query('executionRoleArn'),
              "networkMode": current_task_output.stdout | from_json | json_query('networkMode'),
              "containerDefinitions": current_task_output.stdout | from_json | json_query('containerDefinitions'),
              "volumes": current_task_output.stdout | from_json | json_query('volumes'),
              "placementConstraints": current_task_output.stdout | from_json | json_query('placementConstraints'),
              "requiresCompatibilities": current_task_output.stdout | from_json | json_query('requiresCompatibilities'),
              "cpu": current_task_output.stdout | from_json | json_query('cpu'),
              "memory": current_task_output.stdout | from_json | json_query('memory'),
              "runtimePlatform": current_task_output.stdout | from_json | json_query('runtimePlatform')
            }
          }}

    - name: Update container image in the new task definition
      set_fact:
        updated_task_def: >-
          {{
            new_task_def | combine({
              'containerDefinitions': new_task_def.containerDefinitions | map('combine', {'image': new_image}) | list
            })
          }}

    - name: Save updated task definition to a JSON file
      copy:
        content: "{{ updated_task_def | to_json }}"
        dest: "/tmp/task_def.json"

    - name: Register new ECS task definition
      command: aws ecs register-task-definition --cli-input-json file:///tmp/task_def.json
      register: new_task_output

    - name: Extract new revision number
      set_fact:
        new_revision: "{{ new_task_output.stdout | from_json | json_query('taskDefinition.revision') }}"

    - name: Update ECS service with the new task definition
      command: >
        aws ecs update-service --cluster {{ ecs_cluster }} --service {{ ecs_service }} --task-definition {{ task_family }}:{{ new_revision }}

    - name: Update ECS service with the new task definition
      command: >
        aws ecs update-service --cluster {{ ecs_cluster }} --service {{ ecs_service }} --task-definition {{ task_family }}:{{ new_revision }}

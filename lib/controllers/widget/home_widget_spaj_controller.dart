import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeWidgetSpajController extends BaseControllers {
  RxList<WidgetSpajModels> arrData = RxList();
  RxList<WidgetSpajModels> arrDataIndividu = RxList();
  RxList<WidgetSpajModels> arrDataTeam = RxList();
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;
  RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team
  String userLevel = '';
  String agentName = '';
  late SharedPreferences prefs;

  // Track loading states for both API calls
  bool _individuLoaded = false;
  bool _teamLoaded = false;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    userLevel = prefs.getString(kStorageUserLevel) ?? '';
    agentName = prefs.getString(kStorageAgentName) ?? "";
    setLoading(true);
    load();
  }

  @override
  void load() {
    super.load();
    setLoading(true);

    // Reset loading states
    _individuLoaded = false;
    _teamLoaded = false;

    loadSpajIndividu();

    if (userLevel != kLevelBP) {
      loadSpajTeam();
    } else {
      // For BP level, only individu data is loaded
      _teamLoaded = true;
    }
  }

  void loadSpajIndividu() {
    api.getSpajIndividu(
      controllers: this,
      code: 1,
      params: 'page=0&size=3',
    ); // Request code 1 for individu
  }

  void loadSpajTeam() {
    api.getSpajTeam(
      controllers: this,
      code: 2,
      params: 'page=0&size=3',
    ); // Request code 2 for team
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    updateDisplayData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    updateDisplayData();
  }

  // Update the displayed data based on selected tab
  void updateDisplayData() {
    if (selectedSection.value == 0) {
      arrData.assignAll(arrDataIndividu);
    } else {
      // If team data is empty but we're switching to team tab, try to load it
      if (arrDataTeam.isEmpty && userLevel != kLevelBP) {
        loadSpajTeam();
      }
      arrData.assignAll(arrDataTeam);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Determine if this is an individu or team response based on the request code
    String type = 'individu';

    // If we're handling a team API response
    if (requestCode == 2) {
      // Request code 2 for team API
      type = 'team';
      _teamLoaded = true;
    } else {
      // Request code 1 for individu API
      _individuLoaded = true;
    }

    // Debug the response structure
    _debugResponseStructure(response, type: type);

    parseData(response, type: type);

    // Only set loading to false when both API calls are completed
    if (_individuLoaded && _teamLoaded) {
      setLoading(false);
    }
  }

  // Helper method to debug response structure
  void _debugResponseStructure(dynamic response, {String type = 'individu'}) {
    try {
      if (response is List) {
        // It's a list, check if it has items
        if (response.isNotEmpty) {
          // Check the first item's structure
          final firstItem = response.first;
          if (firstItem is Map) {
            // Log the keys to understand the structure
            // We can log this to a file or use a proper logging framework
            // firstItem.keys.toList();
          }
        }
      } else if (response is Map) {
        // It's a map, check its keys
        // We can log this to a file or use a proper logging framework
        // response.keys.toList();
      }
    } catch (e) {
      // Silently ignore any errors in debug method
    }
  }

  parseData(response, {String type = 'individu'}) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <WidgetSpajModels>[];

      // Handle different response structures
      var data = [];

      if (response is List) {
        data = response;
      } else if (response is Map) {
        // Try to extract data from different possible response structures
        if (response.containsKey('content') && response['content'] is List) {
          data = response['content'];
        } else if (response.containsKey('data') && response['data'] is List) {
          data = response['data'];
        }
      }

      // Process the data
      for (var item in data) {
        try {
          WidgetSpajModels element = WidgetSpajModels.fromJson(item);
          tempList.add(element);
        } catch (e) {
          // Skip items that can't be parsed
        }
      }

      if (type == 'individu') {
        arrDataIndividu.assignAll(tempList);
      } else {
        arrDataTeam.assignAll(tempList);
      }

      // Update display data to maintain current tab selection
      updateDisplayData();

      hasError.value = false;
      errorMessage.value = '';
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = 'Failed to load SPAJ data';
    setLoading(false);
  }

  @override
  void loadError(e, {response}) {
    super.loadError(e, response: response);
    hasError.value = true;
    errorMessage.value = e.toString();
    setLoading(false);
  }
}

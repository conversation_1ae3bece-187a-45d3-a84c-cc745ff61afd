import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/home_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/languages/languages.dart';
import 'package:pdl_superapp/utils/themes/theme_primary.dart';
import '../flavors.dart';

// ignore: use_key_in_widget_constructors
class Dummypage extends StatelessWidget {
  final HomeController controller = Get.put(HomeController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(F.title)),
      body: SizedBox(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          // mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Hello ${F.title}'),
            FilledButton(
              onPressed:
                  () => controller.changeTheme(
                    "Primary Ligth",
                    ThemePrimary.light,
                  ),
              child: Text('Primary Light'),
            ),
            FilledButton(
              onPressed:
                  () =>
                      controller.changeTheme("Primary Dark", ThemePrimary.dark),
              child: Text('Primary dark'),
            ),
            Obx(() => Text('button_home'.tr)),
            ElevatedButton(
              onPressed: () {
                Languages().changeLocale('English');
              },
              child: Text('English'),
            ),
            ElevatedButton(
              onPressed: () {
                Languages().changeLocale('Indonesia');
              },
              child: Text('Indo'),
            ),
            FilledButton(
              onPressed: () => Get.toNamed(Routes.LOGIN),
              child: Text('Login Page'),
            ),
            FilledButton(
              onPressed: () => Get.toNamed(Routes.HOME),
              child: Text('Home Page'),
            ),
          ],
        ),
      ),
    );
  }
}

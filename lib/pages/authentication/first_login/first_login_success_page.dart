import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/pages/authentication/first_login/base_first_login.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class FirstLoginSuccessPage extends StatelessWidget {
  const FirstLoginSuccessPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BaseFirstLogin(
      title: 'title_login_success'.tr,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingExtraLarge,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: Get.width / 1.5,
              child: Utils.cachedSvgWrapper(
                'icon/illustration-empty-user-secure.svg',
              ),
            ),
            Text(
              'title_create_password'.tr,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 20,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: paddingMedium),
            Text(
              'sub_title_login_new_password'.tr,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            Container(
              width: Get.width,
              margin: EdgeInsets.only(top: paddingLarge),
              child: FilledButton(
                onPressed: () => Get.toNamed(Routes.CHANGE_PASSWORD),
                child: Text('label_change_password'.tr),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
